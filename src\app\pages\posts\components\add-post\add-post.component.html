<app-toolbar class="toolbar" [showBack]="true" navigateTo="/activity-submission"></app-toolbar>

<div class="d-flex flex-row-fluid flex-center bg-body rounded mt-4 mt-xl-0">
  <form class="py-20 w-100 w-xl-700px px-9" [formGroup]="storyForm">
    <div class="pb-10 pb-lg-15">
      <h2 class="fw-bolder text-dark"> Submission Info </h2>
    </div>


    <div class="fv-row mb-10">
      <label class="form-label mb-3 required" for="type"> Assignee </label>

      <div class="form-check mb-3 form-check-sm form-check-custom form-check-solid">
        <input class="form-check-input" name="projectType" formControlName='projectType'
          (change)="onRadioTouched($event)" value="member" type="radio" id="type1" checked>
        <label class="form-check-label" for="type1">
          Member
        </label>
      </div>
      <div class="form-check mb-3 form-check-sm form-check-custom form-check-solid">
        <input class="form-check-input" name="projectType" formControlName='projectType'
          (change)="onRadioTouched($event)" value="stakeholder" type="radio" id="type2">
        <label class="form-check-label" for="type2">
          Stakeholder
        </label>
      </div>
      <div class="form-check mb-3 form-check-sm form-check-custom form-check-solid">
        <input class="form-check-input" name="projectType" formControlName='projectType'
          (change)="onRadioTouched($event)" value="student" type="radio" id="type3">
        <label class="form-check-label" for="type3">
          Student
        </label>
      </div>
    </div>
    <div class="fv-row mb-10" *ngIf="type === 'member'">
      <label class="form-label mb-3 required" for="relations"> Member </label>
      <ng-select [compareWith]="compareWith" (change)="onMemberSelect($event, 'member')" formControlName='memberId'
        labelForId="relations" bindLabel="name">
        <ng-option *ngFor="let list of organizationList" [value]="list?.id">
          {{ list?.name | titlecase }}
        </ng-option>
      </ng-select>

      <div class="fv-plugins-message-container invalid-feedback"
        *ngIf="storyForm.get('memberId')?.hasError('required') && (storyForm.get('memberId')?.touched || storyForm.get('memberId')?.dirty)">
        Member is Required.
      </div>
    </div>

    <div class="fv-row mb-10" *ngIf="type === 'stakeholder'">
      <label class="form-label mb-3 required" for="otherPerson"> Stakeholder </label>
      <ng-select [compareWith]="compareWith" (change)="onMemberSelect($event, 'stakeholder')" formControlName='memberId'
        labelForId="relations" bindLabel="name">
        <ng-option *ngFor="let list of stakeholderList" [value]="list?.id">
          {{ list?.name | titlecase }}
        </ng-option>
      </ng-select>

      <div class="fv-plugins-message-container invalid-feedback"
        *ngIf="storyForm.get('memberId')?.hasError('required') && (storyForm.get('memberId')?.touched || storyForm.get('memberId')?.dirty)">
        Stakeholder is Required.
      </div>
    </div>

    <div class="fv-row mb-10" *ngIf="type === 'student'">
      <label class="form-label mb-3 required" for="otherPerson"> Student </label>
      <ng-select [compareWith]="compareWith" (change)="onMemberSelect($event, 'student')" formControlName='memberId'
        labelForId="relations" bindLabel="name">
        <ng-option *ngFor="let list of studentList" [value]="list?.id">
          {{ list?.name | titlecase }}
        </ng-option>
      </ng-select>

      <div class="fv-plugins-message-container invalid-feedback"
        *ngIf="storyForm.get('memberId')?.hasError('required') && (storyForm.get('memberId')?.touched || storyForm.get('memberId')?.dirty)">
        Student is Required.
      </div>
    </div>
    <div class="fv-row mb-10">
      <label class="form-label mb-3" for="otherPerson"> Homework Activity </label>
      <ng-select [compareWith]="compareWith" formControlName='projectId' labelForId="relations" bindLabel="name"
        [loading]="loadingHomework">
        <ng-option *ngFor="let list of projectList" [value]="list?.homeworkData?.id">
          {{ list?.homeworkData?.name | camelcase }}
        </ng-option>
        <ng-option *ngIf="loadingHomework" [disabled]="true">Loading homework activities...</ng-option>
      </ng-select>
    </div>

    <div class="mb-10 fv-row">
      <label class="form-label mb-3 required" for="text"> Submission Title </label>
      <input type="text" formControlName='text' class="form-control form-control-lg form-control-solid"
        placeholder="Submission title" />
      <div class="fv-plugins-message-container invalid-feedback"
        *ngIf="storyForm.get('text')?.hasError('required') && (storyForm.get('text')?.touched || storyForm.get('text')?.dirty)">
        Title is Required.
      </div>
      <div class="fv-plugins-message-container invalid-feedback"
        *ngIf="storyForm.get('text')?.hasError('pattern') && (storyForm.get('text')?.touched || storyForm.get('text')?.dirty)">
        Space is not allowed at the start of the name.
      </div>
    </div>

    <div class="fv-row mb-10">
      <label class="form-label mb-3 required" for="type"> Select Media </label>

      <div class="form-check mb-3 form-check-sm form-check-custom form-check-solid" *ngIf="mediaType!=='description'">
        <input class="form-check-input" name="image"
          [disabled]="(mediaType === 'video' && storyId) && this.postDetails?.videos?.length > 0"
          (change)="onSelectFile($event)" value="image" type="radio" id="img1" [checked]="mediaType === 'image'">
        <label class="form-check-label" for="img1">
          Add Images
        </label>
      </div>
      <div class="form-check mb-3 form-check-sm form-check-custom form-check-solid" *ngIf="mediaType!=='description'">
        <input class="form-check-input" name="video"
          [disabled]="(mediaType === 'image' && storyId) && this.postDetails?.images?.length > 0"
          (change)="onSelectFile($event)" value="video" type="radio" id="vid1" [checked]="mediaType === 'video'">
        <label class="form-check-label" for="vid1">
          Add Video
        </label>
      </div>
      <div class="form-check mb-3 form-check-sm form-check-custom form-check-solid" *ngIf="mediaType!=='description'">
        <input class="form-check-input" name="audio"
          [disabled]="((mediaType === 'image'||mediaType === 'video') && storyId) && this.postDetails?.images?.length > 0"
          (change)="onSelectFile($event)" value="audio" type="radio" id="aud1" [checked]="mediaType === 'audio'">
        <label class="form-check-label" for="aud1">
          Add Audio
        </label>
      </div>
      <div class="form-check mb-3 form-check-sm form-check-custom form-check-solid" *ngIf="mediaType==='description'"
        value="description">
        <input class="form-check-input" name="description" type="radio" id="desc1"
          [checked]="mediaType === 'description'">
        <label class="form-check-label" for="desc1">
          Add Description
        </label>
      </div>
    </div>

    <div class="image-input" data-kt-image-input="true" *ngIf="mediaType!=='audio' && mediaType!=='description'"
      [ngStyle]="{'background-image': !!imageSrc ? 'url(' + imageSrc + ')' : 'url(/assets/media/avatars/placeholder.png)'}"
      [ngClass]="{'image-input-empty': !imageSrc, 'image-input-outline': !!imageSrc, 'mb-10': !this.storyForm.controls['images']?.touched || this.storyForm.controls['videos']?.touched}">
      <div (click)="openImageCarouselModal(imagesModal, mediaType)"
        (keydown.enter)="openImageCarouselModal(imagesModal, mediaType)" class="image-input-wrapper w-125px h-125px"
        [ngStyle]="{'background-image': !!imageSrc ? 'url(' + imageSrc + ')' : 'url(/assets/media/avatars/placeholder.png)'}">
      </div>


      <label class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
        data-kt-image-input-action="change" data-bs-toggle="tooltip" data-bs-dismiss="click" title="Change avatar"
        for="orgLogoInput">
        <em class="bi bi-pencil-fill fs-7"></em>
        <input type="file" name="avatar" multiple id="orgLogoInput" (change)="onChange($event)"
          aria-label="Change avatar" />
        <input type="hidden" name="avatar_remove" />
      </label>
      <span class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
        data-kt-image-input-action="cancel" data-bs-toggle="tooltip" data-bs-dismiss="click" title="Cancel avatar">
        <em class="bi bi-x fs-2"></em>
      </span>
      <span class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
        data-kt-image-input-action="remove" data-bs-toggle="tooltip" data-bs-dismiss="click" (click)="removeFile(true)"
        (keydown.enter)="removeFile(true)" title="Remove avatar">
        <em class="bi bi-x fs-2"></em>
      </span>
    </div>

    <div class="fv-plugins-message-container invalid-feedback mb-10"
      *ngIf="(storyForm.get('images')?.hasError('required') && storyForm.get('images')?.touched) && (storyForm.get('videos')?.hasError('required') && storyForm.get('videos')?.touched) && (storyForm.get('audios')?.hasError('required') && storyForm.get('audios')?.touched)">
      Images, Audio or Video is required.
    </div>

    <div class="fv-row wid-flll" *ngIf="mediaType === 'video' && showingImage?.videoPreview.length > 0">
      <label class="form-label mb-3 " for="type"> Choose Video Thumbnail. </label>
      <div class="card mb-5 mb-xl-10 position-relative ">
        <div class="symbol symbol-100px symbol-lg-160px symbol-fixed position-relative">
          <input style="display: none" type="file" (change)="onFileSelect($event)" #hiddenfileinput>
          <button type="button" (click)="hiddenfileinput.click()" class="btn btn-secondary"><i
              class="fas fa-file-import"></i> Add Custom Thumbnail.</button>
        </div>
        <div class="mar-top" *ngIf="videoThumbnail.length > 0">
          <div class="symbol symbol-100px symbol-lg-160px symbol-fixed position-relative" id="mer-right"
            *ngFor="let videoImage of videoThumbnail" (click)="convertImgToFile(videoImage)"
            (keydown.enter)="convertImgToFile(videoImage)">
            <img [src]="videoImage" class="cursor-pointer" alt="Post" />
          </div>
        </div>
      </div>
    </div>
    <div class="row mb-7" *ngIf="mediaType === 'audio' && storyId">
      <label class="form-label mb-3 required" for=""> Audio </label>
      <div class="col-lg-8">
        <div class="fw-bold fs-6 text-dark mb-3" *ngFor="let audio of postDetails?.audios; index as i;">
          <a class="text-dark text-hover-primary cursor-pointer"
            (click)="openImageCarouselModal(imagesModal, 'audios')">
            {{ audio.substring(audio.lastIndexOf("/") + 1, audio.length) }}
          </a>
          <span class="cursor-pointer p-5" (click)="triggerFileInput()" (keydown.enter)="triggerFileInput()"><i
              class="fas fa-file-import"></i></span>
          <input type="file" accept="audio/*" #audioInput style="display: none" (change)="onChange($event)"
            aria-label="audio">
          <br />
        </div>
      </div>
    </div>

    <div class="mb-10 fv-row" *ngIf="mediaType === 'audio' && !storyId">
      <label class="form-label mb-3 required" for=""> Audio </label>
      <span class="col-lg-4 fw-bold text-muted"> {{audioNames}} </span>
      <span class="cursor-pointer p-5" (click)="triggerFileInput()" (keydown.enter)="triggerFileInput()"><i
          class="fas fa-file-import"></i></span>
      <input type="file" accept="audio/*" #audioInput style="display: none" (change)="onChange($event)">
    </div>

    <div class="mb-10 fv-row">
      <label class="form-label mb-3 required" for="description"> Submission Description </label>
      <input type="text" formControlName='description' class="form-control form-control-lg form-control-solid"
        placeholder="Submission description" />
      <div class="fv-plugins-message-container invalid-feedback"
        *ngIf="storyForm.get('description')?.hasError('required') && (storyForm.get('description')?.touched || storyForm.get('description')?.dirty)">
        Description is Required.
      </div>
      <div class="fv-plugins-message-container invalid-feedback"
        *ngIf="storyForm.get('description')?.hasError('pattern')">
        Description cannot contain space and some of the special characters.
      </div>
    </div>

    <div class="fv-row mb-10">
      <label class="d-flex align-items-center form-label" for="submissionStatus"> Submission Status </label>
      <div>
        <label class="form-check form-check-sm form-check-custom form-check-solid mb-3">
          <input type="radio" name="submissionStatus" formControlName="submissionStatus" class="form-check-input"
            [value]="'APPROVED'">
          <span class="form-check-label"> Approve </span>
        </label>
        <label class="form-check form-check-sm form-check-custom form-check-solid mb-3">
          <input type="radio" name="submissionStatus" formControlName="submissionStatus" class="form-check-input"
            [value]="'INREVIEW'">
          <span class="form-check-label"> In-review </span>
        </label>
        <label class="form-check form-check-sm form-check-custom form-check-solid mb-3">
          <input type="radio" name="submissionStatus" formControlName="submissionStatus" class="form-check-input"
            [value]="'DENIED'">
          <span class="form-check-label"> Deny </span>
        </label>
      </div>
    </div>

    <div class="mb-10 fv-row">
      <label class="form-label mb-3 required" for="grade"> Submission Grade </label>
      <input type="text" formControlName='grade' class="form-control form-control-lg form-control-solid"
        placeholder="0.0" />
      <div class="fv-plugins-message-container invalid-feedback"
        *ngIf="storyForm.get('grade')?.hasError('required') && (storyForm.get('grade')?.touched || storyForm.get('grade')?.dirty)">
        Submission Grade is Required.
      </div>
      <div class="fv-plugins-message-container invalid-feedback"
        *ngIf="storyForm.get('grade')?.hasError('pattern') && (storyForm.get('grade')?.touched || storyForm.get('grade')?.dirty)">
        Please enter a valid grade.
      </div>
    </div>

    <div class="mb-10 fv-row">
      <label class="form-label mb-3" for="feedback"> Feedback </label>
      <input type="text" formControlName='feedback' class="form-control form-control-lg form-control-solid"
        placeholder="Feedback" />
    </div>

    <div class="fv-row mb-10">
      <label class="d-flex align-items-center form-label" for="isPublic"> Submission Type </label>
      <div>
        <label class="form-check form-check-sm form-check-custom form-check-solid mb-3">
          <input type="radio" name="isPublic" formControlName="isPublic" class="form-check-input" [value]='true'>
          <span class="form-check-label"> Public </span>
        </label>
        <label class="form-check form-check-sm form-check-custom form-check-solid mb-3">
          <input type="radio" name="isPublic" formControlName="isPublic" class="form-check-input" [value]='false'>
          <span class="form-check-label"> Private </span>
        </label>
      </div>
    </div>

    <div class="pt-10">
      <button type="button"
              class="btn btn-lg me-3"
              [class.btn-primary]="isFormSubmittable()"
              [class.btn-secondary]="!isFormSubmittable()"
              [disabled]="!isFormSubmittable()"
              (click)="submitPost()">
        <span class="indicator-label"> Submit </span>
      </button>
      <!-- Debug button - remove after fixing -->
      <button type="button"
              class="btn btn-outline-info btn-sm me-2"
              (click)="debugFormState()"
              *ngIf="!isFormSubmittable()">
        Debug Form
      </button>
      <div *ngIf="!isFormSubmittable()" class="mt-2">
        <div class="alert alert-warning py-2" *ngIf="getValidationMessages().length > 0">
          <small class="fw-bold">Please fix the following issues:</small>
          <ul class="mb-0 mt-1">
            <li *ngFor="let message of getValidationMessages()">
              <small>{{ message }}</small>
            </li>
          </ul>
        </div>
        <div class="text-muted" *ngIf="getValidationMessages().length === 0">
          <small>Please fill in all required fields to submit</small>
        </div>
      </div>
    </div>
  </form>
</div>

<!-- -------------------- Post Images Modal --------------------  -->
<ng-template #imagesModal>
  <div class="modal-content" id="detailsModal">
    <div class="modal-header">
      <h2> {{ modal | titlecase }} </h2>
      <div class="btn btn-sm btn-icon btn-active-color-primary">
        <span class="cursor-pointer" (click)="closeModal()" (keydown.enter)="closeModal()">
          <span class="svg-icon svg-icon-1">
            <app-svg-arrow svg="arr061"></app-svg-arrow>
          </span>
        </span>
      </div>
    </div>
    <div class="modal-body text-center">
      <ngb-carousel #carousel [interval]="2000">
        <ng-container *ngIf="modal === 'image'">
          <ng-template ngbSlide *ngFor="let image of showingImage?.imagePreview; index as i">
            <div class="picsum-img-wrapper">
              <img [src]="image" alt="My pic {{i + 1}} description" style="width: 100%;"
                (error)="sharedService.onImgError($event)" />
            </div>
          </ng-template>
        </ng-container>
        <ng-container *ngIf="modal === 'video'">
          <ng-template ngbSlide *ngFor="let video of showingImage?.videoPreview; index as i">
            <div class="picsum-img-wrapper">
              <video width="320" height="240" controls>
                <source src="{{video}}" type="video/mp4">
                <!-- Placeholder subtitle track -->
                <track src="assets/subtitles/placeholder-en.vtt" kind="subtitles" srclang="en" label="English">
                <!-- Placeholder description track -->
                <track src="assets/descriptions/placeholder-desc.vtt" kind="descriptions" srclang="en"
                  label="Descriptions">
              </video>
            </div>
          </ng-template>
        </ng-container>
        <ng-container *ngIf="modal === 'audios'">
          <ng-template ngbSlide *ngFor="let audio of showingImage?.audioPreview; index as i">
            <div class="picsum-img-wrapper audio-wrapper">
              <div class="audio-player-container">
                <div class="audio-icon">
                  <i class="fas fa-music"></i>
                </div>
                <audio controls class="custom-audio-player">
                  <source src="{{audio}}" type="audio/mp3">
                  Your browser does not support the audio element.
                </audio>
              </div>
            </div>
          </ng-template>
        </ng-container>
      </ngb-carousel>
    </div>

    <div class="modal-footer d-flex justify-content-center">
      <button type="button" class="btn btn-outline-primary" (click)="closeModal()"> Close </button>
    </div>
  </div>
</ng-template>
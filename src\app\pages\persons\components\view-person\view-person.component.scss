.alg-end {
    text-align: end;
}

.mar-right {
    margin-right: 1%;
}

.fw-reject-color {
    color: red;
}

.fw-approve-color {
    color: green;
}

.fw-pending-color {
    color: orange;
}

// Enhanced MVT Balance Card Styles
.mvt-balance-card {
    // Increased width to accommodate locked balance information
    min-width: 280px;
    max-width: 320px;

    // Maintain consistent height with other dashboard cards
    min-height: 85px;

    // Responsive width adjustments
    @media (max-width: 1200px) {
        min-width: 260px;
        max-width: 300px;
    }

    @media (max-width: 992px) {
        min-width: 240px;
        max-width: 280px;
    }

    @media (max-width: 768px) {
        min-width: 200px;
        max-width: 100%;
        margin-right: 0 !important;
        margin-bottom: 1rem !important;
    }
}

.mvt-balance-container {
    // Improved container layout
    display: flex;
    flex-direction: column;
    height: 100%;

    // Primary balance section (main MVT balance)
    .primary-balance-section {
        flex: 0 0 auto;
        margin-bottom: 0.25rem;

        .wallet-info-icon {
            font-size: 1.1rem;
            transition: all 0.2s ease;
            opacity: 0.7;

            &:hover {
                color: var(--bs-primary) !important;
                transform: scale(1.1);
                opacity: 1;
            }
        }
    }

    // Secondary balance section (locked and available)
    .secondary-balance-section {
        flex: 1 1 auto;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;

        // Locked balance row styling
        .locked-balance-row {
            border-top: 1px solid rgba(var(--bs-warning-rgb), 0.15);
            padding-top: 0.4rem;
            margin-bottom: 0.3rem;
            background: rgba(var(--bs-warning-rgb), 0.03);
            border-radius: 4px;
            padding: 0.4rem 0.5rem;

            .locked-badge {
                font-size: 0.7rem;
                font-weight: 600;
                padding: 0.2rem 0.4rem;

                .bi-lock-fill {
                    font-size: 0.65rem;
                }
            }

            .locked-info-icon {
                font-size: 0.85rem;
                transition: all 0.2s ease;
                cursor: help;
                opacity: 0.6;

                &:hover {
                    color: var(--bs-warning) !important;
                    transform: scale(1.1);
                    opacity: 1;
                }
            }
        }

        // Available balance row styling
        .available-balance-row {
            margin-top: auto;

            .available-balance {
                font-size: 0.75rem;
                color: var(--bs-gray-600);
                font-weight: 500;
                line-height: 1.2;
                opacity: 0.8;
            }
        }
    }
}

// Enhanced tooltip styles for MVT balance information
.tooltip {
    .tooltip-inner {
        max-width: 280px;
        text-align: left;
        background-color: var(--bs-dark);
        border-radius: 6px;
        padding: 10px 14px;
        font-size: 0.875rem;
        line-height: 1.4;
        white-space: pre-line;
    }
}

// Additional visual enhancements for balance display
.mvt-balance-container {
    // Smooth transitions for all interactive elements
    * {
        transition: all 0.2s ease;
    }

    // Enhanced visual hierarchy
    .primary-balance-section {
        .fs-3 {
            letter-spacing: -0.5px;
            font-weight: 700;
        }

        .badge {
            font-weight: 600;
            letter-spacing: 0.5px;
        }
    }

    // Improved spacing and alignment
    .secondary-balance-section {
        .locked-balance-row {
            transition: all 0.2s ease;

            &:hover {
                background: rgba(var(--bs-warning-rgb), 0.06);
                border-color: rgba(var(--bs-warning-rgb), 0.25);
            }
        }
    }

    // Focus states for accessibility
    .wallet-info-icon:focus,
    .locked-info-icon:focus {
        outline: 2px solid var(--bs-primary);
        outline-offset: 2px;
        border-radius: 2px;
    }
}

// Ensure consistent card heights across the dashboard
.mvt-balance-card {
    display: flex;
    flex-direction: column;

    // Match the height of other dashboard cards
    .mvt-balance-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }
}
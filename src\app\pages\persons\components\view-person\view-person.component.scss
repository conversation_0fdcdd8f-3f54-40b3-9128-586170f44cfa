.alg-end {
    text-align: end;
}

.mar-right {
    margin-right: 1%;
}

.fw-reject-color {
    color: red;
}

.fw-approve-color {
    color: green;
}

.fw-pending-color {
    color: orange;
}

// MVT Balance Styles
.mvt-balance-container {
    .wallet-info-icon {
        font-size: 1.1rem;
        transition: all 0.2s ease;

        &:hover {
            color: var(--bs-primary) !important;
            transform: scale(1.1);
        }
    }

    // Locked balance specific styles
    .locked-balance-row {
        border-top: 1px solid rgba(var(--bs-warning-rgb), 0.2);
        padding-top: 0.5rem;
        margin-top: 0.5rem;
    }

    .locked-badge {
        font-size: 0.75rem;
        font-weight: 600;

        .bi-lock-fill {
            font-size: 0.7rem;
        }
    }

    .locked-info-icon {
        font-size: 0.9rem;
        transition: all 0.2s ease;
        cursor: help;

        &:hover {
            color: var(--bs-warning) !important;
            transform: scale(1.1);
        }
    }

    // Available balance text styling
    .available-balance {
        font-size: 0.8rem;
        color: var(--bs-gray-600);
        font-weight: 500;
    }
}

// Tooltip custom styles
.tooltip {
    .tooltip-inner {
        max-width: 250px;
        text-align: left;
        background-color: var(--bs-dark);
        border-radius: 6px;
        padding: 8px 12px;
        font-size: 0.875rem;
    }
}
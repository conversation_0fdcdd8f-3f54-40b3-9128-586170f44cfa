/**
 * Swap Central Wallet Integration Tests
 * 
 * This test suite verifies the complete swap approval flow including:
 * 1. Central wallet balance updates during swap approval
 * 2. Atomic operations and rollback scenarios
 * 3. Balance verification and persistence
 * 4. Error handling and recovery
 */

const swapService = require('../swap.service');
const walletService = require('../../wallet/wallet.service');
const contractService = require('../../../shared/blockchain/contractService');

// Mock dependencies
jest.mock('../../wallet/wallet.service');
jest.mock('../../../shared/blockchain/contractService');
jest.mock('../../transaction/transaction.service');
jest.mock('../../usdc/usdc.service');
jest.mock('../../../shared/database/dynamoUtils');

const mockWalletService = walletService;
const mockContractService = contractService;

describe('Swap Central Wallet Integration', () => {
  const swapRequestId = 'swap-req-123';
  const adminUserId = 'admin-456';
  const userId = 'user-789';
  const mvtAmount = 100;
  const usdcAmount = 50;

  const mockSwapRequest = {
    id: swapRequestId,
    userId: userId,
    userWalletAddress: '******************************************',
    mvtAmount: mvtAmount,
    usdcAmount: usdcAmount,
    exchangeRate: 0.5,
    status: 'PENDING'
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock successful USDC transfer
    mockContractService.transferUSDCToUser.mockResolvedValue({
      success: true,
      transactionHash: '0xabcdef123456',
      blockNumber: 12345,
      gasUsed: '25000'
    });

    // Mock swap request retrieval
    jest.spyOn(swapService, 'getSwapRequestById').mockResolvedValue(mockSwapRequest);
    
    // Mock swap status update
    jest.spyOn(swapService, 'updateSwapRequestStatus').mockResolvedValue();
  });

  describe('Successful Swap Approval with Central Wallet Update', () => {
    test('should correctly update central wallet balance during swap approval', async () => {
      // Arrange
      const initialCentralBalance = 5000;
      const initialUserBalance = 1000;
      const initialUserLockedBalance = 150;

      // Mock successful MVT transfer to central wallet
      const mvtTransferResult = {
        success: true,
        transferAmount: mvtAmount,
        previousCentralBalance: initialCentralBalance,
        newCentralBalance: initialCentralBalance + mvtAmount,
        previousUserBalance: initialUserBalance,
        newUserBalance: initialUserBalance - mvtAmount,
        previousUserLockedBalance: initialUserLockedBalance,
        newUserLockedBalance: initialUserLockedBalance - mvtAmount
      };

      mockWalletService.transferLockedMVTToCentral.mockResolvedValue(mvtTransferResult);

      // Mock balance verification queries
      mockWalletService.getUserBalance.mockResolvedValue({
        balance: initialUserBalance - mvtAmount,
        lockedBalance: initialUserLockedBalance - mvtAmount
      });

      mockWalletService.getCentralWalletBalance.mockResolvedValue({
        balance: initialCentralBalance + mvtAmount,
        totalReceived: 2000 + mvtAmount
      });

      // Act
      const result = await swapService.approveSwapRequest(swapRequestId, adminUserId);

      // Assert
      expect(result.status).toBe('APPROVED');
      expect(result.mvtAmount).toBe(mvtAmount);
      expect(result.usdcAmount).toBe(usdcAmount);

      // Verify MVT transfer to central wallet was called
      expect(mockWalletService.transferLockedMVTToCentral).toHaveBeenCalledWith(userId, mvtAmount);

      // Verify balance verification was performed
      expect(mockWalletService.getUserBalance).toHaveBeenCalledWith(userId);
      expect(mockWalletService.getCentralWalletBalance).toHaveBeenCalled();

      // Verify USDC transfer was called
      expect(mockContractService.transferUSDCToUser).toHaveBeenCalledWith(
        mockSwapRequest.userWalletAddress,
        usdcAmount
      );
    });

    test('should handle central wallet balance verification failure', async () => {
      // Arrange
      const initialCentralBalance = 5000;
      const mvtTransferResult = {
        success: true,
        transferAmount: mvtAmount,
        previousCentralBalance: initialCentralBalance,
        newCentralBalance: initialCentralBalance + mvtAmount,
        previousUserBalance: 1000,
        newUserBalance: 900
      };

      mockWalletService.transferLockedMVTToCentral.mockResolvedValue(mvtTransferResult);

      // Mock balance verification showing incorrect central balance
      mockWalletService.getUserBalance.mockResolvedValue({
        balance: 900,
        lockedBalance: 50
      });

      mockWalletService.getCentralWalletBalance.mockResolvedValue({
        balance: initialCentralBalance, // Balance didn't increase - verification failure
        totalReceived: 2000
      });

      // Act & Assert
      await expect(
        swapService.approveSwapRequest(swapRequestId, adminUserId)
      ).rejects.toThrow('Central wallet balance verification failed');

      // Verify the transfer was attempted
      expect(mockWalletService.transferLockedMVTToCentral).toHaveBeenCalled();
    });
  });

  describe('Rollback Scenarios', () => {
    test('should handle MVT transfer failure after USDC transfer', async () => {
      // Arrange
      const transferError = new Error('Atomic transaction failed');
      transferError.code = 'TransactionCanceledException';
      
      mockWalletService.transferLockedMVTToCentral.mockRejectedValue(transferError);

      // Act & Assert
      await expect(
        swapService.approveSwapRequest(swapRequestId, adminUserId)
      ).rejects.toThrow('Failed to transfer locked MVT tokens to central wallet');

      // Verify USDC transfer was successful but MVT transfer failed
      expect(mockContractService.transferUSDCToUser).toHaveBeenCalled();
      expect(mockWalletService.transferLockedMVTToCentral).toHaveBeenCalled();
    });

    test('should handle user balance verification failure', async () => {
      // Arrange
      const initialCentralBalance = 5000;
      const mvtTransferResult = {
        success: true,
        transferAmount: mvtAmount,
        previousCentralBalance: initialCentralBalance,
        newCentralBalance: initialCentralBalance + mvtAmount,
        previousUserBalance: 1000,
        newUserBalance: 900
      };

      mockWalletService.transferLockedMVTToCentral.mockResolvedValue(mvtTransferResult);

      // Mock correct central balance but incorrect user balance
      mockWalletService.getUserBalance.mockResolvedValue({
        balance: 1000, // Balance didn't decrease - verification failure
        lockedBalance: 50
      });

      mockWalletService.getCentralWalletBalance.mockResolvedValue({
        balance: initialCentralBalance + mvtAmount,
        totalReceived: 2000 + mvtAmount
      });

      // Act & Assert
      await expect(
        swapService.approveSwapRequest(swapRequestId, adminUserId)
      ).rejects.toThrow('User balance verification failed');
    });
  });

  describe('Atomic Operation Verification', () => {
    test('should ensure atomic transfer of MVT to central wallet', async () => {
      // Arrange
      const mvtTransferResult = {
        success: true,
        transferAmount: mvtAmount,
        previousCentralBalance: 5000,
        newCentralBalance: 5100,
        previousUserBalance: 1000,
        newUserBalance: 900
      };

      mockWalletService.transferLockedMVTToCentral.mockResolvedValue(mvtTransferResult);

      mockWalletService.getUserBalance.mockResolvedValue({
        balance: 900,
        lockedBalance: 50
      });

      mockWalletService.getCentralWalletBalance.mockResolvedValue({
        balance: 5100,
        totalReceived: 2100
      });

      // Act
      const result = await swapService.approveSwapRequest(swapRequestId, adminUserId);

      // Assert
      expect(result.status).toBe('APPROVED');

      // Verify the transfer function was called with correct parameters
      expect(mockWalletService.transferLockedMVTToCentral).toHaveBeenCalledWith(userId, mvtAmount);

      // Verify that the transfer result contains the expected balance changes
      const transferCall = mockWalletService.transferLockedMVTToCentral.mock.calls[0];
      expect(transferCall[0]).toBe(userId);
      expect(transferCall[1]).toBe(mvtAmount);
    });

    test('should handle concurrent modification during atomic transfer', async () => {
      // Arrange
      const concurrentError = new Error('Concurrent modification detected');
      concurrentError.code = 'ConditionalCheckFailedException';
      
      mockWalletService.transferLockedMVTToCentral.mockRejectedValue(concurrentError);

      // Act & Assert
      await expect(
        swapService.approveSwapRequest(swapRequestId, adminUserId)
      ).rejects.toThrow('Failed to transfer locked MVT tokens to central wallet');

      // Verify the atomic operation was attempted
      expect(mockWalletService.transferLockedMVTToCentral).toHaveBeenCalled();
    });
  });

  describe('Balance Persistence Verification', () => {
    test('should verify balance changes persist after swap completion', async () => {
      // Arrange
      const initialCentralBalance = 5000;
      const mvtTransferResult = {
        success: true,
        transferAmount: mvtAmount,
        previousCentralBalance: initialCentralBalance,
        newCentralBalance: initialCentralBalance + mvtAmount,
        previousUserBalance: 1000,
        newUserBalance: 900
      };

      mockWalletService.transferLockedMVTToCentral.mockResolvedValue(mvtTransferResult);

      // Mock multiple balance queries to simulate persistence verification
      mockWalletService.getUserBalance
        .mockResolvedValueOnce({ balance: 900, lockedBalance: 50 })
        .mockResolvedValueOnce({ balance: 900, lockedBalance: 50 }); // Second call for persistence check

      mockWalletService.getCentralWalletBalance
        .mockResolvedValueOnce({ balance: 5100, totalReceived: 2100 })
        .mockResolvedValueOnce({ balance: 5100, totalReceived: 2100 }); // Second call for persistence check

      // Act
      const result = await swapService.approveSwapRequest(swapRequestId, adminUserId);

      // Assert
      expect(result.status).toBe('APPROVED');
      expect(result.newUserBalance).toBe(900);

      // Verify balance queries were made for verification
      expect(mockWalletService.getUserBalance).toHaveBeenCalled();
      expect(mockWalletService.getCentralWalletBalance).toHaveBeenCalled();
    });
  });
});

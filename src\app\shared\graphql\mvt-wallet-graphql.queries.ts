import { gql } from 'apollo-angular';

// MVT Wallet Balance Operations
export const GET_ADMIN_MVT_WALLET_BALANCE = gql`
  query GetAdminMVTWalletBalance {
    getAdminMVTWalletBalance {
      statusCode
      message
      data {
        id
        balance
        totalMinted
        totalTransferred
        lastMintedAt
        createdAt
      }
    }
  }
`;

// Get user's MVT wallet balance
// Regular users: Pass {} or null for input (uses Cognito identity)
// Admins: Pass { userId: "target-user-id" } to check other users' balances
export const GET_USER_MVT_WALLET_BALANCE = gql`
  query GetUserMVTWalletBalance($input: UserBalanceQueryInput) {
    getUserMVTWalletBalance(input: $input) {
      statusCode
      message
      data {
        userId
        balance
        lockedBalance
        availableBalance
        totalReceived
        totalSent
        lastUpdated
        recentTransactions {
          id
          transactionType
          amount
          status
          description
          createdAt
        }
      }
    }
  }
`;

// MVT Wallet Transaction Operations
export const ADMIN_MINT_MVT = gql`
  mutation AdminMintMVT($input: AdminMintMVTInput!) {
    adminMintMVT(input: $input) {
      statusCode
      message
      data {
        id
        transactionType
        amount
        fromWalletId
        toWalletId
        fromUserId
        toUserId
        status
        transactionHash
        internalTxId
        description
        adminUserId
        gasUsed
        gasPrice
        blockNumber
        confirmations
        metadata
        createdAt
        updatedAt
      }
    }
  }
`;

export const ADMIN_TRANSFER_MVT = gql`
  mutation AdminTransferMVT($input: AdminTransferMVTInput!) {
    adminTransferMVT(input: $input) {
      statusCode
      message
      data {
        id
        transactionType
        amount
        fromWalletId
        toWalletId
        fromUserId
        toUserId
        status
        transactionHash
        internalTxId
        description
        adminUserId
        gasUsed
        gasPrice
        blockNumber
        confirmations
        metadata
        createdAt
        updatedAt
      }
    }
  }
`;

export const USER_TRANSFER_MVT = gql`
  mutation UserTransferMVT($input: UserTransferMVTInput!) {
    userTransferMVT(input: $input) {
      statusCode
      message
      data {
        id
        transactionType
        amount
        fromWalletId
        toWalletId
        fromUserId
        toUserId
        status
        transactionHash
        internalTxId
        description
        adminUserId
        gasUsed
        gasPrice
        blockNumber
        confirmations
        metadata
        createdAt
        updatedAt
      }
    }
  }
`;

// MVT Wallet Transaction List Operations
export const GET_MVT_WALLET_TRANSACTION_LIST = gql`
  query GetMVTWalletTransactionList($address: String!, $isAdmin: Boolean, $limit: Int) {
    getMVTWalletTransactionList(address: $address, isAdmin: $isAdmin, limit: $limit) {
      statusCode
      message
      data {
        id
        transactionType
        tokenType
        amount
        fromWalletId
        toWalletId
        fromUserId
        fromUser {
          givenName
          familyName
        }
        toUserId
        toUser {
          givenName
          familyName
        }
        status
        transactionHash
        internalTxId
        description
        adminUserId
        gasUsed
        gasPrice
        blockNumber
        confirmations
        metadata
        createdAt
        updatedAt
        # Display-ready fields from backend
        displayType
        primaryLabel
        secondaryInfo
        showEtherscanLink
        formattedDate
      }
    }
  }
`;

export const GET_MVT_WALLET_TRANSACTION_BY_ID = gql`
  query GetMVTWalletTransactionById($transactionId: ID!) {
    getMVTWalletTransactionById(transactionId: $transactionId) {
      statusCode
      message
      data {
        id
        transactionType
        amount
        fromWalletId
        toWalletId
        fromUserId
        toUserId
        status
        transactionHash
        internalTxId
        description
        adminUserId
        gasUsed
        gasPrice
        blockNumber
        confirmations
        metadata
        createdAt
        updatedAt
      }
    }
  }
`;

// USDC Liquidity Pool Operations
export const GET_USDC_LIQUIDITY_POOL = gql`
  query GetUSDCLiquidityPool {
    getUSDCLiquidityPool {
      statusCode
      message
      data {
        totalReserves
        availableBalance
        adminWalletAddress
        lastUpdated
      }
    }
  }
`;

export const ADMIN_DEPOSIT_USDC = gql`
  mutation AdminDepositUSDC($input: USDCDepositInput!) {
    adminDepositUSDC(input: $input) {
      statusCode
      message
      data {
        id
        transactionType
        amount
        fromWalletId
        toWalletId
        fromUserId
        toUserId
        status
        transactionHash
        internalTxId
        description
        adminUserId
        gasUsed
        gasPrice
        blockNumber
        confirmations
        metadata
        createdAt
        updatedAt
      }
    }
  }
`;

export const ADMIN_WITHDRAW_USDC = gql`
  mutation AdminWithdrawUSDC($input: USDCWithdrawalInput!) {
    adminWithdrawUSDC(input: $input) {
      statusCode
      message
      data {
        id
        transactionType
        amount
        fromWalletId
        toWalletId
        fromUserId
        toUserId
        status
        transactionHash
        internalTxId
        description
        adminUserId
        gasUsed
        gasPrice
        blockNumber
        confirmations
        metadata
        createdAt
        updatedAt
      }
    }
  }
`;

// Exchange Rate Operations
export const GET_MVT_WALLET_EXCHANGE_RATE = gql`
  query GetMVTWalletExchangeRate {
    getMVTWalletExchangeRate {
      statusCode
      message
      data {
        currentRate
        rateDisplay
        liquidityStatus {
          usdcReserves
          mvtSupply
          liquidityRatio
          status
        }
        lastUpdated
      }
    }
  }
`;

// MVT Wallet Swap Operations
export const REQUEST_MVT_WALLET_SWAP = gql`
  mutation RequestMVTWalletSwap($input: SwapRequestInput!) {
    requestMVTWalletSwap(input: $input) {
      statusCode
      message
      data {
        id
        userId
        userWalletAddress
        mvtAmount
        usdcAmount
        exchangeRate
        status
        description
        requestedAt
        expiresAt
      }
    }
  }
`;

export const GET_MVT_WALLET_SWAP_REQUESTS = gql`
  query GetMVTWalletSwapRequests($isAdmin: Boolean, $limit: Int) {
    getMVTWalletSwapRequests(isAdmin: $isAdmin, limit: $limit) {
      statusCode
      message
      data {
        id
        userId
        userName
        userWalletAddress
        mvtAmount
        usdcAmount
        exchangeRate
        status
        description
        requestedAt
        processedAt
        expiresAt
        adminUserId
        rejectionReason
        transactionHash
      }
    }
  }
`;

export const APPROVE_MVT_WALLET_SWAP = gql`
  mutation ApproveMVTWalletSwap($swapRequestId: ID!) {
    approveMVTWalletSwap(input: { swapRequestId: $swapRequestId }) {
      statusCode
      message
      data {
        success
        swapRequestId
        mvtAmount
        usdcAmount
        userWalletAddress
        transactionHash
        newUserBalance
        processedAt
        status
      }
    }
  }
`;

export const REJECT_MVT_WALLET_SWAP = gql`
  mutation RejectMVTWalletSwap($swapRequestId: ID!, $rejectionReason: String!) {
    rejectMVTWalletSwap(input: { swapRequestId: $swapRequestId, rejectionReason: $rejectionReason }) {
      statusCode
      message
      data {
        success
        swapRequestId
        mvtAmount
        usdcAmount
        userWalletAddress
        processedAt
        status
        rejectionReason
      }
    }
  }
`;

export const GET_MVT_WALLET_SWAP_REQUEST_BY_ID = gql`
  query GetMVTWalletSwapRequestById($swapRequestId: String!) {
    getMVTWalletSwapRequestById(swapRequestId: $swapRequestId) {
      statusCode
      message
      data {
        id
        userId
        userWalletAddress
        mvtAmount
        usdcAmount
        exchangeRate
        status
        requestedAt
        processedAt
        description
        rejectionReason
        transactionHash
      }
    }
  }
`;

// Stripe Onramp Operations
export const CREATE_ONRAMP_SESSION = gql`
  mutation CreateOnrampSession($usdcAmount: String!, $userWallet: String!, $mvtAmount: String, $exchangeRate: String) {
    createOnrampSession(usdcAmount: $usdcAmount, userWallet: $userWallet, mvtAmount: $mvtAmount, exchangeRate: $exchangeRate) {
      statusCode
      message
      data {
        sessionId
        clientSecret
        livemode
      }
    }
  }
`;

// Frontend-Triggered Transfer Operations
export const PROCESS_ONRAMP_TRANSFER = gql`
  mutation ProcessOnrampTransfer(
    $userId: String!
    $mvtAmount: Float!
    $usdcAmount: Float!
    $exchangeRate: Float!
    $sessionId: String!
    $description: String
  ) {
    processOnrampTransfer(
      userId: $userId
      mvtAmount: $mvtAmount
      usdcAmount: $usdcAmount
      exchangeRate: $exchangeRate
      sessionId: $sessionId
      description: $description
    ) {
      statusCode
      message
      data {
        mvtTransactionId
        usdcTransactionId
        newBalance
        transferAmount
      }
    }
  }
`;

export const CHECK_ONRAMP_TRANSFER_STATUS = gql`
  query CheckOnrampTransferStatus($sessionId: String!) {
    checkOnrampTransferStatus(sessionId: $sessionId) {
      statusCode
      message
      data {
        processed
        transactionId
        timestamp
      }
    }
  }
`;

export const VERIFY_ONRAMP_SESSION = gql`
  query VerifyOnrampSession($sessionId: String!) {
    verifyOnrampSession(sessionId: $sessionId) {
      statusCode
      message
      data {
        sessionId
        status
        completed
        transactionDetails
      }
    }
  }
`;



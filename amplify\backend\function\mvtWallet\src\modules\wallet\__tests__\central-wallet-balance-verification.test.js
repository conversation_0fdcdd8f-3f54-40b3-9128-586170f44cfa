/**
 * Central Wallet Balance Verification Tests
 * 
 * This test suite verifies that when a swap request is approved:
 * 1. The central wallet balance is correctly updated to reflect MVT tokens received
 * 2. The transfer occurs atomically with user's locked balance deduction
 * 3. The central wallet's totalReceived field is updated for accurate accounting
 * 4. Balance changes are persistent and visible in subsequent queries
 * 5. If central wallet balance update fails, the entire transaction is rolled back
 */

const walletService = require('../wallet.service');
const { createDatabaseLogger } = require('../../../shared/utils/logger');

// Mock AWS DynamoDB
const mockDynamoDB = {
  getItem: jest.fn(),
  updateItem: jest.fn(),
  transactWrite: jest.fn(),
  scan: jest.fn()
};

jest.mock('../../../config/aws', () => ({
  AWS: {
    DynamoDB: {
      Converter: {
        marshall: jest.fn((obj) => obj),
        unmarshall: jest.fn((obj) => obj)
      }
    }
  },
  ddb: mockDynamoDB
}));

jest.mock('../../../shared/database/dynamoUtils', () => ({
  getTableName: jest.fn((name) => `${name}-test-table`),
  tableExists: jest.fn().mockResolvedValue(true)
}));

jest.mock('../../../shared/utils/logger', () => ({
  createDatabaseLogger: jest.fn(() => ({
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn()
  })),
  logError: jest.fn(),
  logSuccess: jest.fn()
}));

describe('Central Wallet Balance Verification', () => {
  const testUserId = 'test-user-123';
  const transferAmount = 100;
  const initialUserBalance = 1000;
  const initialUserLockedBalance = 150;
  const initialCentralBalance = 5000;
  const initialCentralTotalReceived = 2000;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('transferLockedMVTToCentral', () => {
    test('should correctly update central wallet balance atomically', async () => {
      // Arrange
      const mockUserBalance = {
        Item: {
          id: testUserId,
          userId: testUserId,
          balance: initialUserBalance,
          lockedBalance: initialUserLockedBalance,
          totalSent: 0,
          lastUpdated: '2024-01-01T00:00:00.000Z'
        }
      };

      const mockCentralBalance = {
        Item: {
          id: 'central-wallet',
          balance: initialCentralBalance,
          totalReceived: initialCentralTotalReceived,
          totalTransferred: 1000,
          updatedAt: '2024-01-01T00:00:00.000Z'
        }
      };

      // Mock the balance queries
      mockDynamoDB.getItem
        .mockReturnValueOnce({ promise: () => Promise.resolve(mockUserBalance) })
        .mockReturnValueOnce({ promise: () => Promise.resolve({ Items: [] }) }) // user transactions
        .mockReturnValueOnce({ promise: () => Promise.resolve(mockCentralBalance) });

      // Mock successful atomic transaction
      mockDynamoDB.transactWrite.mockReturnValue({
        promise: () => Promise.resolve({})
      });

      // Act
      const result = await walletService.transferLockedMVTToCentral(testUserId, transferAmount);

      // Assert
      expect(result.success).toBe(true);
      expect(result.transferAmount).toBe(transferAmount);
      expect(result.previousCentralBalance).toBe(initialCentralBalance);
      expect(result.newCentralBalance).toBe(initialCentralBalance + transferAmount);
      expect(result.previousUserBalance).toBe(initialUserBalance);
      expect(result.newUserBalance).toBe(initialUserBalance - transferAmount);

      // Verify atomic transaction was called
      expect(mockDynamoDB.transactWrite).toHaveBeenCalledTimes(1);
      
      const transactCall = mockDynamoDB.transactWrite.mock.calls[0][0];
      expect(transactCall.TransactItems).toHaveLength(2);

      // Verify user balance update
      const userUpdate = transactCall.TransactItems[0].Update;
      expect(userUpdate.TableName).toBe('UserMVTBalance-test-table');
      expect(userUpdate.ConditionExpression).toContain('#lockedBalance >= :amount');
      expect(userUpdate.ExpressionAttributeValues[':amount'].N).toBe(transferAmount.toString());

      // Verify central wallet update
      const centralUpdate = transactCall.TransactItems[1].Update;
      expect(centralUpdate.TableName).toBe('MVTTokenWallet-test-table');
      expect(centralUpdate.ExpressionAttributeValues[':newBalance'].N).toBe((initialCentralBalance + transferAmount).toString());
      expect(centralUpdate.ExpressionAttributeValues[':newTotalReceived'].N).toBe((initialCentralTotalReceived + transferAmount).toString());
    });

    test('should handle insufficient locked balance atomically', async () => {
      // Arrange
      const mockUserBalance = {
        Item: {
          id: testUserId,
          balance: initialUserBalance,
          lockedBalance: 50, // Less than transfer amount
          totalSent: 0
        }
      };

      const mockCentralBalance = {
        Item: {
          id: 'central-wallet',
          balance: initialCentralBalance,
          totalReceived: initialCentralTotalReceived
        }
      };

      mockDynamoDB.getItem
        .mockReturnValueOnce({ promise: () => Promise.resolve(mockUserBalance) })
        .mockReturnValueOnce({ promise: () => Promise.resolve({ Items: [] }) })
        .mockReturnValueOnce({ promise: () => Promise.resolve(mockCentralBalance) });

      // Act & Assert
      await expect(
        walletService.transferLockedMVTToCentral(testUserId, transferAmount)
      ).rejects.toThrow('Insufficient locked balance');

      // Verify no atomic transaction was attempted
      expect(mockDynamoDB.transactWrite).not.toHaveBeenCalled();
    });

    test('should handle atomic transaction failure with proper error', async () => {
      // Arrange
      const mockUserBalance = {
        Item: {
          id: testUserId,
          balance: initialUserBalance,
          lockedBalance: initialUserLockedBalance,
          totalSent: 0
        }
      };

      const mockCentralBalance = {
        Item: {
          id: 'central-wallet',
          balance: initialCentralBalance,
          totalReceived: initialCentralTotalReceived
        }
      };

      mockDynamoDB.getItem
        .mockReturnValueOnce({ promise: () => Promise.resolve(mockUserBalance) })
        .mockReturnValueOnce({ promise: () => Promise.resolve({ Items: [] }) })
        .mockReturnValueOnce({ promise: () => Promise.resolve(mockCentralBalance) });

      // Mock transaction cancellation (atomic failure)
      const transactionError = new Error('Transaction cancelled');
      transactionError.code = 'TransactionCanceledException';
      mockDynamoDB.transactWrite.mockReturnValue({
        promise: () => Promise.reject(transactionError)
      });

      // Act & Assert
      await expect(
        walletService.transferLockedMVTToCentral(testUserId, transferAmount)
      ).rejects.toThrow('Transfer failed: Insufficient locked balance or concurrent modification detected');

      // Verify atomic transaction was attempted
      expect(mockDynamoDB.transactWrite).toHaveBeenCalledTimes(1);
    });

    test('should update totalReceived field correctly', async () => {
      // Arrange
      const mockUserBalance = {
        Item: {
          id: testUserId,
          balance: initialUserBalance,
          lockedBalance: initialUserLockedBalance,
          totalSent: 0
        }
      };

      const mockCentralBalance = {
        Item: {
          id: 'central-wallet',
          balance: initialCentralBalance,
          totalReceived: initialCentralTotalReceived
        }
      };

      mockDynamoDB.getItem
        .mockReturnValueOnce({ promise: () => Promise.resolve(mockUserBalance) })
        .mockReturnValueOnce({ promise: () => Promise.resolve({ Items: [] }) })
        .mockReturnValueOnce({ promise: () => Promise.resolve(mockCentralBalance) });

      mockDynamoDB.transactWrite.mockReturnValue({
        promise: () => Promise.resolve({})
      });

      // Act
      const result = await walletService.transferLockedMVTToCentral(testUserId, transferAmount);

      // Assert
      const transactCall = mockDynamoDB.transactWrite.mock.calls[0][0];
      const centralUpdate = transactCall.TransactItems[1].Update;
      
      // Verify totalReceived is updated correctly
      expect(centralUpdate.UpdateExpression).toContain('#totalReceived = :newTotalReceived');
      expect(centralUpdate.ExpressionAttributeNames['#totalReceived']).toBe('totalReceived');
      expect(centralUpdate.ExpressionAttributeValues[':newTotalReceived'].N).toBe((initialCentralTotalReceived + transferAmount).toString());
    });
  });

  describe('Balance Persistence Verification', () => {
    test('should maintain balance consistency across multiple operations', async () => {
      // This test would verify that balance changes persist across multiple queries
      // Implementation would involve multiple sequential operations and balance checks
      
      // Arrange
      const operations = [
        { userId: 'user1', amount: 50 },
        { userId: 'user2', amount: 75 },
        { userId: 'user3', amount: 25 }
      ];

      let expectedCentralBalance = initialCentralBalance;
      
      for (const operation of operations) {
        // Mock balances for each operation
        const mockUserBalance = {
          Item: {
            id: operation.userId,
            balance: 1000,
            lockedBalance: 200,
            totalSent: 0
          }
        };

        const mockCentralBalance = {
          Item: {
            id: 'central-wallet',
            balance: expectedCentralBalance,
            totalReceived: initialCentralTotalReceived
          }
        };

        mockDynamoDB.getItem
          .mockReturnValueOnce({ promise: () => Promise.resolve(mockUserBalance) })
          .mockReturnValueOnce({ promise: () => Promise.resolve({ Items: [] }) })
          .mockReturnValueOnce({ promise: () => Promise.resolve(mockCentralBalance) });

        mockDynamoDB.transactWrite.mockReturnValue({
          promise: () => Promise.resolve({})
        });

        // Act
        const result = await walletService.transferLockedMVTToCentral(operation.userId, operation.amount);

        // Assert
        expect(result.newCentralBalance).toBe(expectedCentralBalance + operation.amount);
        expectedCentralBalance += operation.amount;
      }
    });
  });
});

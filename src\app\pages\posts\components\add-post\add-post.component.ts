import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { Location } from '@angular/common';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { NgxImageCompressService } from 'ngx-image-compress';
import { ActivatedRoute, Router } from '@angular/router';
import { Storage } from 'aws-amplify';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { NgxSpinnerService } from 'ngx-spinner';
import { forkJoin, lastValueFrom, map } from 'rxjs';
import { HttpClient } from '@angular/common/http';

import { UsersService } from 'src/app/pages/organizations/services/users.service';
import { SharedService } from 'src/app/shared/services/shared.service';
import { OrganizationsService } from 'src/app/pages/organizations/services/organizations.service';
import { BusinessesService } from 'src/app/pages/businesses/services/businesses.service';
import { PostsService } from '../../services/posts.service';
import { PersonsService } from 'src/app/pages/persons/services/persons.service';
import { PersonToPersonService } from 'src/app/pages/person-to-person/services/person-to-person.service';
import { StakeholderKnowledgeService } from 'src/app/pages/persons/components/person-knowledge-repository/services/stakeholder-knowledge.service';
import { TranscribeService } from '../../services/transcribe.service';
import { MvtWalletService } from 'src/app/shared/services/mvt-wallet.service';

@Component({
  selector: 'app-add-post',
  templateUrl: './add-post.component.html',
  styleUrls: ['./add-post.component.scss'],
})
export class AddPostComponent implements OnInit {
  @ViewChild('orgLogoInput') orgLogoInput: ElementRef;
  @ViewChild('audioInput') audioInputRef!: ElementRef<HTMLInputElement>;
  storyForm: FormGroup = new FormGroup({});
  imageSrc: string;
  personsList: any[] = [];
  organizationList: any[] = [];
  businessList: any[] = [];
  projectList: any[] = [];
  loadingHomework: boolean = false;
  type: string = 'organization';
  url: any;
  format: any;
  mediaType: string = 'image';
  compressedImageFile: File;
  imgResultAfterCompress: string;
  selectedMemberName: any;
  storyId: string;
  files: any[] = [];
  modal: string = '';
  postDetails: any;
  videoThumbnail: any[] = [];
  usersList: any[] = [];
  uploadImages: any[] = [];
  videoThumbnailFile: any[] = [];
  thumbnailName: string = '';
  showingImage: any = {
    imagePreview: [],
    videoPreview: [],
    audioPreview: [],
  };
  loggedInUserId: string = '';
  loggedInUserName: string = '';
  associationList: any[] = [];
  userImage: string = '';
  allUsers: any[] = [];
  categorytype: string = '';
  stakeholderList: any[] = [];
  studentList: any[] = [];
  categoryList: any = {};
  audioNames: any = [];

  constructor(
    private readonly userService: UsersService,
    private readonly organizationsService: OrganizationsService,
    public readonly sharedService: SharedService,
    private readonly businessService: BusinessesService,
    private readonly location: Location,
    private readonly formBuilder: FormBuilder,
    private readonly spinner: NgxSpinnerService,
    private readonly toastr: ToastrService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly postService: PostsService,
    private readonly imageCompress: NgxImageCompressService,
    private readonly router: Router,
    private readonly modalService: NgbModal,
    private readonly personsService: PersonsService,
    private readonly personToPersonService: PersonToPersonService,
    private readonly stakeholderKnowledgeService: StakeholderKnowledgeService,
    private readonly transcribeService: TranscribeService,
    private readonly http: HttpClient,
    private readonly mvtWalletService: MvtWalletService
  ) { }

  ngOnInit(): void {
    this.sharedService.isLoading.next(true);
    this.spinner.show();

    this.storyId = this.activatedRoute.snapshot.paramMap.get('id') ?? '';
    this.initializeForm();
    this.getPersonsList();
    this.getOrganizations();
    this.getUsers();
    this.getAllUsers();
    this.sharedService.listCategories().pipe(
      map(response => response.data.listCategories.items),
      map(items =>
        items
          .filter((item: any) => !item._deleted)
          .map((item: any) => ({
            name: item.name.trim(),
            id: item.id
          }))
      )
    ).subscribe((response: any) => {
      response.forEach((item: any) => {
        this.categoryList[item.name] = item.id;
      });
    });
  }

  initializeForm() {
    this.storyForm = this.formBuilder.group({
      cityId: this.sharedService.defaultCityId.value,
      createdBy: '',
      text: ['', [Validators.required, Validators.pattern(new RegExp(/^(?![\s'-])[\w\s'-]+$/))]],
      grade: ['', [Validators.required, Validators.pattern(new RegExp(/^100(\.00?)?$|^\d{1,2}(\.\d{1,2})?$/))]],
      videos: [[]],  // Remove required validator - will be handled dynamically
      images: [[]],  // Remove required validator - will be handled dynamically
      audios: [[]],  // Remove required validator - will be handled dynamically
      projectType: ['organization'],
      categoryType: ['Health & Wellness'],
      memberId: [null, Validators.required],
      description: ['', [Validators.pattern(new RegExp(/^(?!\s)[a-zA-Z,'‘’\s]+$/))]],
      // description: ['', [Validators.required, Validators.pattern(new RegExp(/^(?![\s'-])[\w\s'-]+$/))]],
      projectId: [null],
      submissionStatus: 'INREVIEW',
      isPublic: [true],
      videoDuration: [null],
      videosThumbnail: [],
      userPostId: '',
      organizationsPostId: '',
      businessPostId: '',
      gradedBy: '',
      feedback: '',
      isDeleted: 'false',
      _version: 0,
    });
  }

  getUsers(): void {
    this.sharedService.getUserData().subscribe((res: any) => {
      this.loggedInUserId = res['custom:dynamodbId'];
      this.loggedInUserName = res?.name;
      if (!this.storyId) {
        this.storyForm.patchValue({
          cityId: this.sharedService.defaultCityId.value,
          createdBy: res['custom:dynamodbId'],
          gradedBy: res?.name
        });
      }
      else {

        this.storyForm.patchValue({
          cityId: this.sharedService.defaultCityId.value,
        });
      }
    });
  }

  openImageCarouselModal(content: any, value: string) {
    if (
      this.showingImage.imagePreview.length > 0 ||
      this.showingImage.videoPreview.length > 0 ||
      this.showingImage.audioPreview.length > 0
    ) {
      this.modal = value;

      this.modalService.open(content, {
        size: 'md',
      });
    }
  }

  getStoryById(): void {
    this.postService.getPostById(this.storyId).subscribe({
      next: (res: any) => {
        this.postDetails = res?.data?.getSubmission;
        this.type = this.postDetails?.projectType;

        this.setMediaType();
        this.loadImages();
        this.loadVideoThumbnails();
        this.loadVideos();
        this.loadAudios();
        this.setDescriptionValidatorIfNeeded();

        this.patchStoryForm(res);
        this.patchMemberRelatedFields(res);

        this.setThumbnailName();

        this.loadHomeworkAssignments(res);
        this.checkMemberExistence();

        this.sharedService.isLoading.next(false);
        this.spinner.hide();
      },
      error: (err) => {
        this.spinner.hide();
        this.sharedService.isLoading.next(false);
        // Optionally handle error here
      },
    });

    // Hide spinner & loading in case of immediate error or end of call
    this.spinner.hide();
    this.sharedService.isLoading.next(false);
  }

  // Helpers

  private setMediaType(): void {
    if (this.postDetails?.videos?.length > 0) {
      this.mediaType = 'video';
    } else if (this.postDetails?.audios?.length > 0) {
      this.mediaType = 'audio';
    }
  }

  private loadImages(): void {
    if (!this.postDetails?.images) return;

    this.userImage = `post/${this.storyId}/images/${this.postDetails.images[0]}`;
    this.postDetails.images.forEach((image: any) => {
      const path = `post/${this.storyId}/images/${image}`;
      Storage.get(path, { level: 'public' }).then((result: string) => {
        this.showingImage.imagePreview?.push(result);
        this.imageSrc = this.showingImage.imagePreview[0];
      });
    });
  }

  private loadVideoThumbnails(): void {
    const thumbnails = this.postDetails?.videosThumbnail;
    if (!thumbnails || thumbnails[0] === '') return;

    this.userImage = `post/${this.storyId}/images/${thumbnails[0]}`;
    thumbnails.forEach((thumb: any) => {
      const path = `post/${this.storyId}/videos-thumbnail/${thumb}`;
      Storage.get(path, { level: 'public' }).then((result: string) => {
        this.showingImage.imagePreview?.push(result);
        this.imageSrc = this.showingImage.imagePreview[0];
      });
    });
  }

  private loadVideos(): void {
    if (!this.postDetails?.videos) return;

    this.postDetails.videos.forEach((video: any, index: number) => {
      const path = `post/${this.storyId}/videos/${video}`;
      Storage.get(path, { level: 'public' }).then((result: string) => {
        this.showingImage.videoPreview?.push(result);

        if (index === this.postDetails.videos.length - 1) {
          this.spinner.hide();
          this.sharedService.isLoading.next(false);
        }
      });
    });
  }

  private loadAudios(): void {
    if (!this.postDetails?.audios) return;

    this.postDetails.audios.forEach((audio: any, index: number) => {
      const path = `post/${this.storyId}/audios/${audio}`;
      Storage.get(path, { level: 'public' }).then((result: string) => {
        this.showingImage.audioPreview?.push(result);

        if (index === this.postDetails.audios.length - 1) {
          this.spinner.hide();
          this.sharedService.isLoading.next(false);
        }
      });
    });
  }

  private setDescriptionValidatorIfNeeded(): void {
    const submissionType = Object.values(this.postDetails?.homework?.submissionType ?? {});
    if (submissionType.every((sub: any) => !sub)) {
      this.mediaType = 'description';
      let control = this.storyForm?.get('description');
      const existingValidators = control?.validator ? [control.validator] : [];
      control?.setValidators([...existingValidators, Validators.required]);
      control?.updateValueAndValidity();
    }
  }

  private patchStoryForm(res: any): void {
    this.storyForm.patchValue({
      text: res?.data?.getSubmission?.text,
      grade: res?.data?.getSubmission?.grade,
      projectId: res?.data?.getSubmission?.projectId,
      projectType: res?.data?.getSubmission?.projectType,
      categoryType: res?.data?.getSubmission?.categoryType,
      submissionStatus: res?.data?.getSubmission?.submissionStatus,
      memberId: res?.data?.getSubmission?.memberId ?? null,
      _version: res?.data?.getSubmission?._version,
      images: this.postDetails.images,
      videoDuration: this.postDetails?.videoDuration,
      isPublic: this.postDetails?.isPublic,
      createdBy: res?.data?.getSubmission?.createdBy,
      gradedBy: res?.data?.getSubmission?.gradedBy,
      feedback: res?.data?.getSubmission?.feedback,
      videos: this.postDetails.videos,
      audios: this.postDetails.audios,
      description: this.postDetails.description,
    });
  }

  private patchMemberRelatedFields(res: any): void {
    if (this.postDetails?.projectType === 'member') {
      this.storyForm.patchValue({
        organizationsPostId: res?.data?.getSubmission?.memberId ?? 'null',
        businessPostId: 'null',
        userPostId: 'null',
      });
    } else {
      this.storyForm.patchValue({
        userPostId: res?.data?.getSubmission?.memberId ?? 'null',
        organizationsPostId: 'null',
        businessPostId: 'null',
      });
    }
  }

  private setThumbnailName(): void {
    if (this.postDetails?.videosThumbnail?.length) {
      this.thumbnailName = this.postDetails.videosThumbnail[0];
    }
  }

  private loadHomeworkAssignments(res: any): void {
    if (!res?.data?.getSubmission?.memberId) return;

    this.loadingHomework = true;

    const memberId = res.data.getSubmission.memberId;
    const callback = {
      next: (data: any) => {
        const items = this.type === 'member' ?
          data.data.listHomeworkOrganizations.items :
          data.data.listHomeworkUsers.items;

        this.projectList = items.filter((item: any) => !item?._deleted);
        this.type = res.data.getSubmission.projectType;
        this.categorytype = res.data.getSubmission.categoryType;
        this.loadingHomework = false;
      },
      error: (error: any) => {
        this.toastr.error(`Error fetching homework assignments: ${error.message}`);
        this.loadingHomework = false;
      }
    };

    if (this.type === 'member') {
      this.postService.getAllHomeworkMember(memberId).subscribe(callback);
    } else {
      this.postService.getAllHomeworkUser(memberId).subscribe(callback);
    }
  }

  private checkMemberExistence(): void {
    const memberId = this.storyForm.get('memberId')?.value;
    let exists = false;

    if (this.type === 'student') {
      exists = this.studentList.some(p => p.id === memberId);
      if (!exists) this.handleMemberNotFound('Student');
    } else if (this.type === 'member') {
      exists = this.organizationList.some(o => o.id === memberId);
      if (!exists) this.handleMemberNotFound('Member');
    } else {
      exists = this.stakeholderList.some(s => s.id === memberId);
      if (!exists) this.handleMemberNotFound('Stakeholder');
    }

    // Debug logging for member existence
    console.log('Member existence check:', {
      memberId: memberId,
      type: this.type,
      exists: exists,
      studentListLength: this.studentList.length,
      organizationListLength: this.organizationList.length,
      stakeholderListLength: this.stakeholderList.length
    });
  }

  private handleMemberNotFound(role: string): void {
    const memberId = this.storyForm.get('memberId')?.value;
    console.error(`Member not found - ${role} with ID ${memberId} has been deleted or is not available.`);
    this.toastr.error(`${role} has been deleted or is no longer available. Please select a different member.`);
    this.storyForm.get('memberId')?.reset();
    this.storyForm.get('projectId')?.reset();
  }

  getAllUsers(): void {
    forkJoin([this.sharedService.getUserList(true), this.sharedService.getUserList()]).subscribe((data: any) => {
      this.allUsers =
        (data[0]?.data?.userByDate?.items ?? []).concat(
          data[1]?.data?.userByDate?.items ?? []
        );
      this.stakeholderList = data[0]?.data?.userByDate?.items?.filter((stake: any) => stake.isStakeholder && stake?.isDeleted === 'false');
      this.studentList = data[1]?.data?.userByDate?.items?.filter((student: any) => !student.isStakeholder && student.isDeleted === 'false');
      if (this.storyId) {
        setTimeout(() => {
          this.getStoryById();
        }, 2000);
      } else {
        this.spinner.hide();
        this.sharedService.isLoading.next(false);
      }
    });
  }

  getPersonsList() {
    this.userService.getExistingUsers().subscribe({
      next: ({ data }: any) => {
        this.personsList = data?.userByDate?.items.filter(
          (element: any) =>
            element.id !== this.sharedService?.userData?.value['custom:dynamodbId'] &&
            element.cityId === this.sharedService?.defaultCityId?.value &&
            !element._deleted
        );
      },
      error: (error: any) => {
        this.location.back();
      },
    });
  }

  getOrganizations(): void {
    this.organizationsService.getOrganizationsList().subscribe((res: any) => {
      this.organizationList = res?.data?.organizationsByDate?.items;
      this.organizationList = this.organizationList.filter(
        (data: any) =>
          data !== null &&
          data?.cityId === this.sharedService.defaultCityId.value
      );
    });
  }

  closeModal() {
    this.modalService.dismissAll();
  }

  getBusinessList() {
    this.sharedService.isLoading.next(false);
    this.businessService.getBusinessList().subscribe(({ data }) => {
      this.businessList = data.businessByDate.items.filter(
        (element: any) =>
          !element?._deleted &&
          this.sharedService.defaultCityId.value === element?.cityId
      );
      if (!this.storyId) {
        this.sharedService.isLoading.next(false);
        this.spinner.hide();
      }
    });
  }

  onMemberSelect(event: any, selectedType: any): void {
    const memberId = event;
    this.storyForm.get('projectId')?.reset();
    if (selectedType === 'member') {
      this.allUsers = [];
      this.selectedMemberName = this.organizationList.find(
        (data: any) => data.id === memberId
      );
      this.storyForm.patchValue({
        organizationsPostId: memberId,
        userPostId: 'null',
        businessPostId: 'null',
      });
      this.getRelationshipsList(memberId);

    } else if (selectedType === 'business') {
      this.allUsers = [];
      this.selectedMemberName = this.businessList.find(
        (data: any) => data.id === memberId
      );
      this.storyForm.patchValue({
        businessPostId: memberId,
        userPostId: 'null',
        organizationsPostId: 'null',
      });
      this.getRelationshipsList(memberId);
    } else {
      this.allUsers = [];
      this.selectedMemberName = this.personsList.find(
        (data: any) => data.id === memberId
      );
      this.storyForm.patchValue({
        userPostId: memberId,
        businessPostId: 'null',
        organizationsPostId: 'null',
      });

      this.allUsers.push(this.selectedMemberName)
      this.getRelationshipsList(memberId);

    }

    if (memberId) {
      this.projectList = [];
      this.loadingHomework = true;

      if (selectedType === 'member') {
        this.postService.getAllHomeworkMember(memberId).subscribe({
          next: ((data: any) => {
            this.projectList = data.data.listHomeworkOrganizations.items.filter(
              (element: any) =>
                !element?._deleted && element.homeworkStatus === "pending"
            );
            if (this.projectList.length === 0) {
              this.toastr.warning(`No Homework assignments found for this ${selectedType} !`)
            }
            this.loadingHomework = false;
          }),
          error: ((error: any) => {
            this.toastr.error(`Error fetching homework assignments: ${error.message}`);
            this.loadingHomework = false;
          })
        })
      } else {
        this.postService.getAllHomeworkUser(memberId).subscribe({
          next: ((data: any) => {
            this.projectList = data.data.listHomeworkUsers.items.filter(
              (element: any) =>
                !element?._deleted && element.homeworkStatus === "pending"
            );
            if (this.projectList.length === 0) {
              this.toastr.warning(`No Homework assignments found for this ${selectedType} !`)
            }
            this.loadingHomework = false;
          }),
          error: ((error: any) => {
            this.toastr.error(`Error fetching homework assignments: ${error.message}`);
            this.loadingHomework = false;
          })
        })
      }

    } else {
      this.personsList = [];
    }
  }

  getRelationshipsList(id: string) {

    this.sharedService?.setNotificationCheckClicked(true)
    this.personToPersonService
      .getPersonToPersonRelationshipsList(id, this.type)
      .subscribe({
        next: ({ data }: any) => {

          this.associationList = data?.associationsByDate?.items.filter((element: any) => element);
          if (this.type === 'organization') {
            this.associationList = data?.associationsByDate?.items.filter((element: any) => element && !element?._deleted && !element?.business?._deleted && !element?.person?._deleted && element.person
              && !element?.organization?._deleted && element?.cityId === this.sharedService?.defaultCityId?.value
              && (((element.type === 'Person' && element.otherPersonsID && element.otherPerson) ||
                (element.type === "Business" || element.type === "Organization") && !element.otherPersonsID && !element?.otherPerson))
            );
            this.associationList?.forEach((element: any) => {
              if (element !== null) {
                this.allUsers.push(element?.person)
              }
            });
          }
          else if (this.type === 'person') {
            this.associationList = data?.associationsByDate?.items.filter((element: any) => element && !element?._deleted && !element?.business?._deleted && !element?.person?._deleted && element.person
              && !element?.organization?._deleted && element?.cityId === this.sharedService?.defaultCityId?.value
              && ((element.type === 'Person' && element.otherPersonsID && element.otherPerson))
            );
            this.associationList?.forEach((element: any) => {
              if (element !== null) {
                if (id === element?.personsID) {
                  this.allUsers.push(element?.otherPerson);
                } else {
                  this.allUsers.push(element?.person);
                }
              }
            });
          }




        },
        error: (error: any) => {
        },
      });
  }

  onRadioTouched(event: any): void {
    this.type = event?.target?.value;
    this.projectList = [];
    this.storyForm.get('memberId')?.reset();
    this.storyForm.get('userPostId')?.reset();
    this.storyForm.get('organizationsPostId')?.reset();
    this.storyForm.get('businessPostId')?.reset();
    this.storyForm.get('projectId')?.reset();
  }

  async onChange(event: any) {
    this.resetPreviews();

    const files: any = event.target.files;
    if (files.length > 5) {
      this.toastr.error('Images should not be more than 5!');
      return;
    }
    this.files = files;

    try {
      if (this.mediaType === 'image') {
        await this.handleImages(files);
      } else if (this.mediaType === 'audio') {
        await this.handleAudio(files);
      } else {
        await this.handleVideos(files);
      }
    } catch (error) {
      console.error(error)
    }
  }

  private resetPreviews() {
    this.showingImage.imagePreview = [];
    this.showingImage.videoPreview = [];
    this.showingImage.audioPreview = [];
    this.videoThumbnail = [];
  }

  private async handleImages(files: FileList) {
    let imageList: string[] = [];
    let lastImageSrc = '';

    for (const file of Array.from(files)) {
      if (!file.type.includes('image')) {
        this.clearInputAndError('Please select images!');
        return;
      }

      const imageSizeMB = +(file.size / (1024 * 1024)).toFixed(1);

      if (imageSizeMB > 10) {
        this.clearInputAndError('File size must not exceed 10MB!');
        return;
      }

      const ratio = this.getCompressionRatio(imageSizeMB);

      const fileSrc = await this.readFileAsDataURL(file);
      lastImageSrc = fileSrc;
      this.showingImage.imagePreview.push(fileSrc);

      if (imageSizeMB > 3) {
        await this.compressFile(fileSrc, file.name, ratio);
        const compressedFileName = this.buildFileName('image_cropper', this.compressedImageFile, file);
        imageList.push(compressedFileName);
        this.uploadImages.push(this.compressedImageFile);
      } else {
        const originalFileName = this.buildFileName('image_cropper', file, file);
        imageList.push(originalFileName);
        this.uploadImages.push(file);
      }
    }

    this.patchFormValues('images', imageList);
    setTimeout(() => { this.imageSrc = lastImageSrc; }, 300);
  }

  private async handleAudio(files: FileList) {
    if (files.length === 0) return;
    const file = files[0];

    if (!file.type.includes('audio')) {
      this.clearInputAndError('Please select an audio!');
      return;
    }

    const audioSizeMB = +(file.size / (1024 * 1024)).toFixed(1);
    if (audioSizeMB > 15) {
      this.toastr.error('File size must not exceed 15MB!');
      return;
    }

    const audioList = [this.buildFileName('AUD', file, file)];
    this.audioNames = audioList.toString();

    const duration = await this.getMediaDuration(file, 'audio');
    this.patchFormValues('videoDuration', duration);

    const fileSrc = await this.readFileAsDataURL(file);
    this.showingImage.audioPreview.push(fileSrc);

    this.patchFormValues('audios', audioList);
    this.clearErrors(['images', 'videos', 'audios']);
  }

  private async handleVideos(files: FileList) {
    if (files.length === 0) return;
    const file = files[0];

    if (!file.type.includes('video')) {
      this.clearInputAndError('Please select a video!');
      return;
    }

    const videoSizeMB = +(file.size / (1024 * 1024)).toFixed(1);
    if (videoSizeMB > 15) {
      this.toastr.error('File size must not exceed 15MB!');
      return;
    }

    const videoList = [this.buildFileName('VID', file, file)];
    const duration = await this.getMediaDuration(file, 'video');
    this.patchFormValues('videoDuration', duration);

    const fileSrc = await this.readFileAsDataURL(file);
    this.showingImage.videoPreview.push(fileSrc);

    const fileUrl = URL.createObjectURL(file);
    const streamingTimes = [0.25, 0.6, 0.9];
    this.videoThumbnail = [];
    streamingTimes.forEach(time => this.getThumbnailForVideo(fileUrl, time));

    this.patchFormValues('videos', videoList);
    this.clearErrors(['images', 'videos']);
  }

  private clearInputAndError(message: string) {
    this.orgLogoInput.nativeElement.value = this.imageSrc = '';
    this.toastr.error(message);
  }

  private buildFileName(prefix: string, file: any, originalFile: File) {
    const extension = originalFile.name.split('.').pop();
    const lastModified = file.lastModified ?? Date.now();
    return `${prefix}_${lastModified}.${extension}`;
  }

  private getCompressionRatio(sizeMB: number): number {
    if (sizeMB > 5 && sizeMB <= 7) return 80;
    if (sizeMB > 3 && sizeMB <= 5) return 90;
    return 70;
  }

  private readFileAsDataURL(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = e => resolve(e.target?.result as string);
      reader.onerror = e => reject(new Error(e.target?.error?.message ?? 'Failed to read file'));
      reader.readAsDataURL(file);
    });
  }

  private getMediaDuration(file: File, type: 'audio' | 'video'): Promise<number> {
    return new Promise((resolve) => {
      const media = document.createElement(type);
      media.preload = 'metadata';
      media.onloadedmetadata = () => {
        resolve(Math.floor(media.duration));
        URL.revokeObjectURL(media.src);
      };
      media.src = URL.createObjectURL(file);
    });
  }

  private patchFormValues(field: string, value: any) {
    this.storyForm.patchValue({ [field]: value });
  }

  private clearErrors(fields: string[]) {
    fields.forEach(field => {
      this.storyForm.controls[field]?.setErrors(null);
    });
  }


  onFileSelect(event: any): void {
    let file = event?.target?.files[0];
    if (!file.type.includes('image')) {
      this.orgLogoInput.nativeElement.value = this.imageSrc = '';
      this.toastr.error('Please select an image for thumbnail!');
      return;
    }

    if (file) {
      const reader: FileReader = new FileReader();

      let ratio: number = 70;
      let needToCompress: boolean = false;

      let imageSize: number = parseFloat(
        (file.size / (1024 * 1024)).toFixed(1)
      );

      reader.readAsDataURL(file);

      if (imageSize > 3 && imageSize <= 5) {
        ratio = 90;
      }

      if (imageSize > 5 && imageSize <= 7) {
        ratio = 80;
      }

      reader.onload = async (fileLoadedEvent: any) => {
        this.imageSrc = fileLoadedEvent.target.result;
        this.showingImage.imagePreview.push(fileLoadedEvent.target.result);
        if (parseFloat((file.size / (1024 * 1024)).toFixed(1)) > 10) {
          this.toastr.error('File size must not exceed 10MB!');
          this.orgLogoInput.nativeElement.value = this.imageSrc = '';
        } else {
          needToCompress = imageSize > 3;

          if (needToCompress) {
            await this.compressFile(this.imageSrc, file?.name, ratio);
            this.thumbnailName =
              'image_cropper' +
              '_' +
              this.compressedImageFile?.lastModified +
              '.' +
              this.compressedImageFile?.name.split('.')[1];
            this.videoThumbnailFile.push(this.compressedImageFile);
          } else {
            this.thumbnailName =
              'image_cropper' +
              '_' +
              file?.lastModified +
              '.' +
              file?.name.split('.')[1];
            this.videoThumbnailFile.push(file);
          }
        }
      };
    }
  }

  async getThumbnailForVideo(videoUrl: any, time: any) {
    let seekDone: boolean = false;
    const video: any = document.createElement('video');
    const canvas: any = document.createElement('canvas');
    video.style.display = 'none';
    canvas.style.display = 'none';

    // Trigger video load
    await new Promise((resolve: any, reject: any) => {
      video.addEventListener('loadedmetadata', () => {
        video.width = video.videoWidth;
        video.height = video.videoHeight;
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        // Seek the video according to time
        video.currentTime = video.duration * time;
      });
      video.addEventListener('seeked', () => {
        seekDone = true;
        resolve();
      });
      video.src = videoUrl;
    });

    // Draw the thumbnailz

    setTimeout(() => {
      if (seekDone) {
        canvas
          .getContext('2d')
          .drawImage(video, 0, 0, video.videoWidth, video.videoHeight);
        let imageUrl = '';
        imageUrl = canvas.toDataURL('image/png');
        if (imageUrl) {
          this.videoThumbnail.push(imageUrl);
        }
        seekDone = false;
      }
    }, 500);
  }

  dataURLtoBlobData(dataurl: string) {
    const [mime, bstr] = dataurl.split(',');
    const n = bstr.length;
    const u8arr = new Uint8Array(n);
    for (let i = 0; i < n; i++) {
      u8arr[i] = bstr.charCodeAt(i);
    }
    return new Blob([u8arr], { type: mime });
  }

  async convertImgToFile(imgFileUrl: any): Promise<void> {
    this.videoThumbnailFile = [];
    this.thumbnailName = '';
    let today = new Date();
    this.imageSrc = imgFileUrl;
    let file = null;
    file = this.dataURLtoBlobData(imgFileUrl);
    this.videoThumbnailFile.push(file);
    this.thumbnailName =
      'image_cropper' +
      '_' +
      today.getTime() +
      file?.size +
      '.' +
      file?.type.split('/')[1];
  }

  async compressFile(image: string, fileName: string, ratio: number) {
    await this.imageCompress
      .compressFile(image, -1, ratio, 50)
      .then((result) => {
        this.imgResultAfterCompress = result;

        const imageName = fileName;
        const imageBlob = this.dataURItoBlob(
          this.imgResultAfterCompress.split(',')[1]
        );

        this.compressedImageFile = new File([imageBlob], imageName, {
          type: 'image/png',
        });
      });
  }

  removeFile(btnClicked?: any) {
    if (btnClicked) {
      this.mediaType == 'image'
        ? this.toastr?.error('Images has been removed!')
        : this.toastr.error('video has been removed!');
    }
    if (this.mediaType)
      this.orgLogoInput.nativeElement.value = this.imageSrc = '';
    this.showingImage.imagePreview = [];
    this.showingImage.videoPreview = [];
    this.showingImage.audioPreview = [];
    this.videoThumbnail = [];
    this.storyForm.patchValue({
      images: [],
      videos: [],
      audios: [],
      videosThumbnail: [],
      videoDuration: null,
    });

    // Clear any validation errors for media fields
    ['images', 'videos', 'audios'].forEach((key: string) => {
      this.storyForm.controls[key]?.setErrors(null);
      this.storyForm.controls[key]?.updateValueAndValidity();
    });
    this.thumbnailName = '';
  }

  dataURItoBlob(dataURI: string) {
    const byteString = window.atob(dataURI);
    const arrayBuffer = new ArrayBuffer(byteString.length);
    const int8Array = new Uint8Array(arrayBuffer);

    for (let i = 0; i < byteString.length; i++) {
      int8Array[i] = byteString.charCodeAt(i);
    }

    const blob: Blob = new Blob([int8Array], { type: 'image/jpeg' });

    return blob;
  }

  onSelectFile(event: any) {
    this.mediaType = event?.target?.value;
    this.removeFile();
  }

  compareWith(item: any, selected: any) {
    let res = false;

    if (item && selected) {
      res = item === selected;
    }

    return res;
  }

  async submitPost(): Promise<void> {
    if (this.thumbnailName) {
      let thumbName: any[] = [];
      thumbName.push(this.thumbnailName);
      this.storyForm.patchValue({
        videosThumbnail: thumbName,
      });
    } else {
      this.storyForm.patchValue({
        videosThumbnail: [],
      });
    }

    // Clear any previous media validation errors since we handle them differently
    this.storyForm.controls['images']?.setErrors(null);
    this.storyForm.controls['videos']?.setErrors(null);
    this.storyForm.controls['audios']?.setErrors(null);

    const keys = ['text', 'grade', 'description', 'memberId'];

    keys.forEach((key: string) => {
      this.storyForm.get(key)?.markAllAsTouched();
      this.storyForm.updateValueAndValidity();
    });

    if (this.storyForm.valid) {
      if (this.storyId) {
        this.updatePost();
      } else {
        this.addPost();
      }
    } else {
      // Display validation errors to the user
      this.displayValidationErrors();
      // Debug form state for troubleshooting
      this.debugFormState();
    }
  }

  private displayValidationErrors(): void {
    const errors: string[] = [];

    // Check required fields
    if (this.storyForm.get('text')?.hasError('required')) {
      errors.push('Submission Title is required');
    } else if (this.storyForm.get('text')?.hasError('pattern')) {
      errors.push('Submission Title contains invalid characters');
    }

    if (this.storyForm.get('grade')?.hasError('required')) {
      errors.push('Grade is required');
    } else if (this.storyForm.get('grade')?.hasError('pattern')) {
      errors.push('Grade must be a valid number between 0-100');
    }

    if (this.storyForm.get('memberId')?.hasError('required')) {
      errors.push('Please select a member');
    }

    // Check media requirements
    const hasImages = this.storyForm.get('images')?.value?.length > 0;
    const hasVideos = this.storyForm.get('videos')?.value?.length > 0;
    const hasAudios = this.storyForm.get('audios')?.value?.length > 0;
    const hasDescription = this.storyForm.get('description')?.value?.trim();

    if (!hasImages && !hasVideos && !hasAudios && !hasDescription) {
      errors.push('Please add at least one image, video, audio, or description');
    }

    if (this.storyForm.get('description')?.hasError('required')) {
      errors.push('Description is required for this submission type');
    } else if (this.storyForm.get('description')?.hasError('pattern')) {
      errors.push('Description contains invalid characters');
    }

    // Display errors
    if (errors.length > 0) {
      const errorMessage = errors.join(', ');
      this.toastr.error(errorMessage, 'Validation Error');
      console.log('Form validation errors:', errors);
      console.log('Form status:', this.storyForm.status);
      console.log('Form errors:', this.getFormValidationErrors());
    }
  }

  private getFormValidationErrors(): any {
    const formErrors: any = {};
    Object.keys(this.storyForm.controls).forEach(key => {
      const controlErrors = this.storyForm.get(key)?.errors;
      if (controlErrors) {
        formErrors[key] = controlErrors;
      }
    });
    return formErrors;
  }

  isFormSubmittable(): boolean {
    // Check basic required fields
    const hasText = this.storyForm.get('text')?.value?.trim();
    const hasGrade = this.storyForm.get('grade')?.value;
    const hasMember = this.storyForm.get('memberId')?.value;
    const memberValid = hasMember ? this.isMemberValid() : false;

    // Check if at least one media type or description is provided
    const hasImages = this.storyForm.get('images')?.value?.length > 0;
    const hasVideos = this.storyForm.get('videos')?.value?.length > 0;
    const hasAudios = this.storyForm.get('audios')?.value?.length > 0;
    const hasDescription = this.storyForm.get('description')?.value?.trim();

    const hasMedia = hasImages || hasVideos || hasAudios || hasDescription;

    // Check pattern validations
    const textValid = !this.storyForm.get('text')?.hasError('pattern');
    const gradeValid = !this.storyForm.get('grade')?.hasError('pattern');
    const descriptionValid = !this.storyForm.get('description')?.hasError('pattern');

    const isSubmittable = hasText && hasGrade && hasMember && memberValid && hasMedia && textValid && gradeValid && descriptionValid;

    return isSubmittable;
  }

  // Check if the selected member is valid and available
  isMemberValid(): boolean {
    const memberId = this.storyForm.get('memberId')?.value;
    if (!memberId) return false;

    if (this.type === 'student') {
      return this.studentList.some(p => p.id === memberId);
    } else if (this.type === 'member') {
      return this.organizationList.some(o => o.id === memberId);
    } else {
      return this.stakeholderList.some(s => s.id === memberId);
    }
  }

  // Get detailed validation messages for user display
  getValidationMessages(): string[] {
    const messages: string[] = [];

    // Check required fields
    if (!this.storyForm.get('text')?.value?.trim()) {
      messages.push('Submission Title is required');
    } else if (this.storyForm.get('text')?.hasError('pattern')) {
      messages.push('Submission Title contains invalid characters');
    }

    if (!this.storyForm.get('grade')?.value) {
      messages.push('Grade is required');
    } else if (this.storyForm.get('grade')?.hasError('pattern')) {
      messages.push('Grade must be a valid number between 0-100');
    }

    if (!this.storyForm.get('memberId')?.value) {
      messages.push('Please select a member');
    } else if (!this.isMemberValid()) {
      messages.push('Selected member is no longer available. Please select a different member.');
    }

    // Check media requirements
    const hasImages = this.storyForm.get('images')?.value?.length > 0;
    const hasVideos = this.storyForm.get('videos')?.value?.length > 0;
    const hasAudios = this.storyForm.get('audios')?.value?.length > 0;
    const hasDescription = this.storyForm.get('description')?.value?.trim();

    if (!hasImages && !hasVideos && !hasAudios && !hasDescription) {
      messages.push('Please add at least one image, video, audio, or description');
    }

    if (this.storyForm.get('description')?.hasError('required')) {
      messages.push('Description is required for this submission type');
    } else if (this.storyForm.get('description')?.hasError('pattern')) {
      messages.push('Description contains invalid characters');
    }

    return messages;
  }

  // Debug method to help identify form issues
  debugFormState(): void {
    console.log('=== FORM DEBUG INFO ===');
    console.log('Form valid:', this.storyForm.valid);
    console.log('Form status:', this.storyForm.status);
    console.log('Form submittable:', this.isFormSubmittable());
    console.log('Form values:', this.storyForm.value);
    console.log('Form errors:', this.getFormValidationErrors());
    console.log('Validation messages:', this.getValidationMessages());

    // Detailed validation breakdown
    const hasText = this.storyForm.get('text')?.value?.trim();
    const hasGrade = this.storyForm.get('grade')?.value;
    const hasMember = this.storyForm.get('memberId')?.value;
    const memberValid = hasMember ? this.isMemberValid() : false;
    const hasImages = this.storyForm.get('images')?.value?.length > 0;
    const hasVideos = this.storyForm.get('videos')?.value?.length > 0;
    const hasAudios = this.storyForm.get('audios')?.value?.length > 0;
    const hasDescription = this.storyForm.get('description')?.value?.trim();
    const hasMedia = hasImages || hasVideos || hasAudios || hasDescription;
    const textValid = !this.storyForm.get('text')?.hasError('pattern');
    const gradeValid = !this.storyForm.get('grade')?.hasError('pattern');
    const descriptionValid = !this.storyForm.get('description')?.hasError('pattern');

    console.log('Validation breakdown:', {
      hasText: !!hasText,
      hasGrade: !!hasGrade,
      hasMember: !!hasMember,
      memberValid: memberValid,
      hasMedia: hasMedia,
      textValid: textValid,
      gradeValid: gradeValid,
      descriptionValid: descriptionValid,
      textValue: this.storyForm.get('text')?.value,
      gradeValue: this.storyForm.get('grade')?.value,
      memberIdValue: this.storyForm.get('memberId')?.value,
      imagesLength: this.storyForm.get('images')?.value?.length || 0,
      videosLength: this.storyForm.get('videos')?.value?.length || 0,
      audiosLength: this.storyForm.get('audios')?.value?.length || 0,
      descriptionValue: this.storyForm.get('description')?.value,
      textErrors: this.storyForm.get('text')?.errors,
      gradeErrors: this.storyForm.get('grade')?.errors,
      descriptionErrors: this.storyForm.get('description')?.errors,
      type: this.type,
      studentListLength: this.studentList.length,
      organizationListLength: this.organizationList.length,
      stakeholderListLength: this.stakeholderList.length
    });

    // Check individual field states
    const fields = ['text', 'grade', 'memberId', 'images', 'videos', 'audios', 'description'];
    fields.forEach(field => {
      const control = this.storyForm.get(field);
      console.log(`${field}:`, {
        value: control?.value,
        valid: control?.valid,
        errors: control?.errors,
        touched: control?.touched,
        dirty: control?.dirty
      });
    });
    console.log('=== END DEBUG INFO ===');
  }

  async uploadFile(file: any, fileName: any, type: any, hasThumbnail: any) {
    this.spinner.show();
    this.sharedService.isLoading.next(true);
    if (type === 'image') {
      try {
        await Storage.put(
          'post/' + this.storyId + '/' + 'images/' + fileName,
          file,
          {
            level: 'public',
            contentType: file?.type,
          }
        ).then((result) => {
          this.userImage = result?.key;
        });
      } catch (error) {
        this.toastr.error('Error uploading file' + error);
      }
    } else if (type === 'audio') {
      try {
        await Storage.put(
          'post/' + this.storyId + '/' + 'audios/' + fileName,
          file,
          {
            level: 'public',
            contentType: file?.type,
          }
        ).then((result) => { });
      } catch (error) {
        this.toastr.error('Error uploading file' + error);
      }
    } else {
      try {
        await Storage.put(
          'post/' + this.storyId + '/' + 'videos/' + fileName,
          file,
          {
            level: 'public',
            contentType: file?.type,
          }
        ).then((result) => { });
      } catch (error) {
        this.toastr.error('Error uploading file' + error);
      }
      if (hasThumbnail == 'true') {
        try {
          await Storage.put(
            'post/' + this.storyId + '/' + 'videos-thumbnail/' + fileName,
            file,
            {
              level: 'public',
              contentType: file?.type,
            }
          ).then((result) => {
            this.userImage = result?.key;
          });
        } catch (error) {
          this.toastr.error('Error uploading file' + error);
        }
      }
    }
  }

  addPost(): void {
    this.spinner.show();
    this.sharedService.isLoading.next(true);
    this.storyForm.removeControl('_version');
    this.storyForm.get('gradedBy')?.patchValue(this.loggedInUserName);
    let formData = this.storyForm.value;
    formData.viewCount = 0;
    formData.pointsAssigned = true;

    const updateHomeworkStatus = (entity: any, status: string, callback: (res: any) => void) => {
      let updateFn: any;
      if (status === 'completed' || status === 'in-review' || status === 'denied') {
        updateFn = entity.id.includes('user') ? this.postService.updateHomeworkUsers : this.postService.updateHomeworkOrganizations;
      }
      if (!updateFn) return;
      updateFn.call(this.postService, {
        id: entity.id,
        homeworkStatus: status,
        homeworkCompletedDate: status === 'completed' ? new Date().toISOString() : undefined,
        _version: entity._version,
      }).subscribe({
        next: callback,
      });
    };

    const handleDeniedActivities = (homeworkType: 'user' | 'member', memberId: string) => {
      this.postService.getActivitiesList(this.storyId, memberId).subscribe((result: any) => {
        if (result?.data?.activitiesByDate?.items.length) {
          for (const activity of result.data.activitiesByDate.items) {
            if (activity?.id && activity?._version) {
              const activityUpdate = {
                id: activity.id,
                requestStatus: 'ADMIN_DENIED',
                _version: activity._version,
              };
              this.sharedService.updateActivity(activityUpdate).subscribe();
            }
          }
        }
      });
    };

    this.postService.createPost(this.storyForm.value).subscribe({
      next: (res: any) => {
        const submission = res?.data?.createSubmission;
        const homework = submission?.homework;

        const homeworkMembers = homework?.members?.items.find(
          (m: any) => formData.projectId === m.homeworkId && formData.memberId === m.memberId
        );
        const homeworkUsers = homework?.studentsStakeholders?.items.find(
          (s: any) => formData.projectId === s.homeworkId && formData.memberId === s.studentStakeholderId
        );

        this.storyId = submission?.id;

        if (this.uploadImages.length > 0 || this.files.length > 0) {
          this.handleFileUploads();
        }

        this.updateStoriesMembers(submission?.memberId, 'CREATE', submission?.pointsAssigned);

        const status = submission?.submissionStatus;
        if (status === 'APPROVED') {
          if (homeworkUsers?.id) {
            updateHomeworkStatus(homeworkUsers, 'completed', (response) => this.addSubmissionPoints(response, 'user'));
          } else if (homeworkMembers?.id) {
            updateHomeworkStatus(homeworkMembers, 'completed', (response) => this.addSubmissionPoints(response, 'member'));
          }
        } else if (status === 'INREVIEW') {
          if (homeworkUsers?.id) {
            updateHomeworkStatus(homeworkUsers, 'in-review', (response) =>
              this.createSubmissionActivityLog(response, submission, 'user')
            );
          } else if (homeworkMembers?.id) {
            updateHomeworkStatus(homeworkMembers, 'in-review', (response) =>
              this.createSubmissionActivityLog(response, submission, 'member')
            );
          }
        } else if (status === 'DENIED') {
          if (homeworkUsers?.id) {
            updateHomeworkStatus(homeworkUsers, 'denied', () => {
              handleDeniedActivities('user', homeworkUsers.id);
            });
          } else if (homeworkMembers?.id) {
            updateHomeworkStatus(homeworkMembers, 'denied', () => {
              handleDeniedActivities('member', homeworkMembers.id);
            });
          }
        }

        this.sharedService
          .generateLog({
            type: 'CREATED',
            moduleId: submission?.id,
            moduleName: submission?.text ?? 'story',
            moduleType: 'homework-submission',
            relatedTo: this.type === 'organization' ? 'member' : this.type,
            relatedId: submission?.memberId ?? '',
            moduleImageUrl: this.userImage ?? '',
            relatedName: this.selectedMemberName?.name,
            activityType: 'STORYTELLING',
            requestStatus: 'SYSTEM_APPROVED',
            cityId: this.sharedService.defaultCityId.value,
            activityTokens: '25',
          })
          .subscribe(() => {
            this.toastr.success('Submission added successfully!');
            this.spinner.hide();
            this.sharedService.isLoading.next(false);
            this.router.navigate(['/activity-submission']);
          });
      },
      error: () => {
        this.spinner.hide();
        this.sharedService.isLoading.next(false);
      },
    });
  }

  private handleFileUploads() {
    if (this.mediaType === 'image') {
      for (let fileData of this.uploadImages) {
        let fileName = 'image_cropper_' + fileData?.lastModified + '.' + fileData?.name.split('.')[1];
        this.uploadFile(fileData, fileName, 'image', 'false');
      }
    } else if (this.mediaType === 'audio') {
      let fileName = 'AUD_' + this.files[0]?.lastModified + '.' + this.files[0]?.name.split('.')[1];
      this.uploadFile(this.files[0], fileName, 'audio', 'false');
    } else {
      let fileName = 'VID_' + this.files[0]?.lastModified + '.' + this.files[0]?.name.split('.')[1];
      this.uploadFile(this.files[0], fileName, 'video', 'false');
      if (this.videoThumbnailFile.length > 0 && this.thumbnailName) {
        this.uploadFile(this.videoThumbnailFile[0], this.thumbnailName, 'video', 'true');
      }
    }
  }


  createSubmissionActivityLog(response: any, submissionData: any, type: any) {
    let relatedId, relatedName, relatedTo, relationHomeworkId;
    if (type === 'member') {
      relationHomeworkId = response?.data?.updateHomeworkOrganizations?.id;
      relatedName = response?.data?.updateHomeworkOrganizations?.memberData?.name;
      relatedTo = 'member';
      relatedId = response?.data?.updateHomeworkOrganizations?.memberId;
    } else {
      relationHomeworkId = response?.data?.updateHomeworkUsers?.id;
      relatedName = response?.data?.updateHomeworkUsers?.studentStakeholderData?.name
      relatedTo = response?.data?.updateHomeworkUsers?.studentStakeholderData?.isStakeholder ? 'stakeholder' : 'student';
      relatedId = response?.data?.updateHomeworkUsers?.studentStakeholderId;
    }

    this.sharedService
      .generateLog({
        type: 'ADDED',
        moduleId: submissionData?.id,
        moduleName: submissionData?.text,
        moduleType: 'homework-submission',
        relatedTo: relatedTo,
        relatedId: relatedId,
        updatedData: { id: relationHomeworkId },
        relatedName: relatedName,
        activityType: 'MEMBERSHIP',
        requestStatus: 'PENDING_APPROVAL',
        cityId: this.sharedService.defaultCityId.value,
        activityTokens: submissionData?.homework?.assignmentPoints
      })
      .subscribe((log) => {
      });
  }

  addSubmissionPoints(response: any, type: any): void {
    let memberId: any, impactScore: number, entityType, microcredentialType, currentImpactScore: any, MVPTokens: any, membershipId: any, version: any;
    if (type === 'member') {
      memberId = response?.data?.updateHomeworkOrganizations?.memberId;
      impactScore = response?.data?.updateHomeworkOrganizations?.homeworkData?.assignmentPoints;
      entityType = 'member';
      microcredentialType = response?.data?.updateHomeworkOrganizations?.homeworkData?.microcredential?.type;
      MVPTokens = response?.data?.updateHomeworkOrganizations?.memberData?.membership?.MVPTokens;
      currentImpactScore = response?.data?.updateHomeworkOrganizations?.memberData?.membership?.currentImpactScore;
      membershipId = response?.data?.updateHomeworkOrganizations?.memberData?.membership?.id;
      version = response?.data?.updateHomeworkOrganizations?.memberData?.membership?._version
    } else {
      memberId = response?.data?.updateHomeworkUsers?.studentStakeholderId;
      impactScore = response?.data?.updateHomeworkUsers?.homeworkData?.assignmentPoints;
      entityType = response?.data?.updateHomeworkUsers?.studentStakeholderData?.isStakeholder ? 'stakeholder' : 'student';
      microcredentialType = response?.data?.updateHomeworkUsers?.homeworkData?.microcredential?.type;
      MVPTokens = response?.data?.updateHomeworkUsers?.studentStakeholderData?.membership?.MVPTokens;
      currentImpactScore = response?.data?.updateHomeworkUsers?.studentStakeholderData?.membership?.currentImpactScore;
      membershipId = response?.data?.updateHomeworkUsers?.studentStakeholderData?.membership?.id;
      version = response?.data?.updateHomeworkUsers?.studentStakeholderData?.membership?._version
    }
    this.sharedService.addPoints(
      {
        memberId: memberId,
        impactScore: impactScore,
        MVPTokens: impactScore,
        pointType: 'homework',
        status: 'CREDITED',
        type: entityType,
        cityId: this.sharedService.defaultCityId.value,
        createdBy: this.loggedInUserId,
        category: microcredentialType,
        categoryID: this.categoryList[microcredentialType?.trim()] ?? Object.values(this.categoryList)[0]
      },
    ).subscribe({
      next: (result => {
        this.transferMVT(memberId, impactScore);
        this.sharedService.updateMembership({
          id: membershipId,
          currentImpactScore: parseFloat(currentImpactScore) + impactScore,
          MVPTokens: parseFloat(MVPTokens) + (impactScore),
          lastAddedImpactScore: impactScore,
          _version: version,
        });
      })
    })
    //get activity by date
    this.postService.getActivitiesList(this.storyId, memberId).subscribe((result: any) => {
      if (result?.data?.activitiesByDate?.items.length > 0) {
        for (const activity of (result?.data?.activitiesByDate?.items ?? [])) {
          let activityUpdate = {
            id: activity?.id,
            requestStatus: 'ADMIN_APPROVED',
            _version: activity?._version,
          };

          this.sharedService.updateActivity(activityUpdate).subscribe((resActivity: any) => {
            this.addMicrocredentialPoints(response, type)
          });

        }
      }
    })
  }

  addMicrocredentialPoints(response: any, type: any): void {
    let microcredentialId: any, memberId: any, microcredentialpoint: any, assignmentPoints: any, entityType: any, microcredentialType: any, membershipId: any, currentImpactScore: any, MVPTokens: any, version: any;
    if (type === 'member') {
      microcredentialId = response?.data?.updateHomeworkOrganizations?.homeworkData?.microcredential?.id;
      microcredentialpoint = response?.data?.updateHomeworkOrganizations?.homeworkData?.microcredential?.totalPoints;
      memberId = response?.data?.updateHomeworkOrganizations?.memberId;
      assignmentPoints = response?.data?.updateHomeworkOrganizations?.homeworkData?.assignmentPoints ?? 0;
      entityType = 'member';
      microcredentialType = response?.data?.updateHomeworkOrganizations?.homeworkData?.microcredential?.type;
      membershipId = response?.data?.updateHomeworkOrganizations?.memberData?.membership?.id;
      currentImpactScore = response?.data?.updateHomeworkOrganizations?.memberData?.membership?.currentImpactScore;
      MVPTokens = response?.data?.updateHomeworkOrganizations?.memberData?.membership?.MVPTokens;
      version = response?.data?.updateHomeworkOrganizations?.memberData?.membership?._version;

      this.postService.getHomeworkMembersByMicrocredential(memberId, microcredentialId).subscribe({
        next: ((data: any) => {
          let homeworkUsersData = data.data.listHomeworkOrganizations.items.filter(
            (element: any) =>
              !element?._deleted && element?.homeworkStatus === "completed"
          );
          let completedHomeworkPoints = homeworkUsersData.reduce((prev: any, curr: any) => prev + Number(curr?.homeworkData?.assignmentPoints), 0);
          let oldCompletedHomeworkPoints = Number(completedHomeworkPoints) - Number(assignmentPoints);
          this.sendMessage(response?.data?.updateHomeworkOrganizations?.homeworkData?.name, 'approve', response?.data?.updateHomeworkOrganizations);
          if (microcredentialpoint > oldCompletedHomeworkPoints && microcredentialpoint <= completedHomeworkPoints) {
            this.sharedService.addPoints(
              {
                memberId: memberId,
                impactScore: microcredentialpoint,
                MVPTokens: microcredentialpoint,
                pointType: 'microcredential',
                status: 'CREDITED',
                type: entityType,
                cityId: this.sharedService.defaultCityId.value,
                createdBy: this.loggedInUserId,
                category: microcredentialType,
                categoryID: this.categoryList[microcredentialType?.trim()] ?? Object.values(this.categoryList)[0]
              },
            ).subscribe({
              next: (result => {
                this.transferMVT(memberId, microcredentialpoint);
                this.sharedService.updateMembership({
                  id: membershipId,
                  currentImpactScore: parseFloat(currentImpactScore) + microcredentialpoint,
                  MVPTokens: parseFloat(MVPTokens) + (microcredentialpoint),
                  lastAddedImpactScore: microcredentialpoint,
                  _version: version,
                });
              })
            })
          }
        })
      })
    } else {
      microcredentialId = response?.data?.updateHomeworkUsers?.homeworkData?.microcredential?.id;
      microcredentialpoint = response?.data?.updateHomeworkUsers?.homeworkData?.microcredential?.totalPoints;
      memberId = response?.data?.updateHomeworkUsers?.studentStakeholderId;
      assignmentPoints = response?.data?.updateHomeworkUsers?.homeworkData?.assignmentPoints ?? 0;
      entityType = response?.data?.updateHomeworkUsers?.studentStakeholderData?.isStakeholder ? 'stakeholder' : 'student';
      microcredentialType = response?.data?.updateHomeworkUsers?.homeworkData?.microcredential?.type;
      membershipId = response?.data?.updateHomeworkUsers?.studentStakeholderData?.membership?.id;
      currentImpactScore = response?.data?.updateHomeworkUsers?.studentStakeholderData?.membership?.currentImpactScore;
      MVPTokens = response?.data?.updateHomeworkUsers?.studentStakeholderData?.membership?.MVPTokens;
      version = response?.data?.updateHomeworkUsers?.studentStakeholderData?.membership?._version

      this.postService.getHomeworkUsersByMicrocredential(memberId, microcredentialId).subscribe({
        next: ((data: any) => {
          let homeworkUsersData = data.data.listHomeworkUsers.items.filter(
            (element: any) =>
              !element?._deleted && element?.homeworkStatus === "completed"
          );

          let completedHomeworkPoints = homeworkUsersData.reduce((prev: any, curr: any) => prev + Number(curr?.homeworkData?.assignmentPoints), 0);

          let oldCompletedHomeworkPoints = Number(completedHomeworkPoints) - Number(assignmentPoints);
          this.sendMessage(response?.data?.updateHomeworkUsers?.homeworkData?.name, 'approve', response?.data?.updateHomeworkUsers);
          if (microcredentialpoint > oldCompletedHomeworkPoints && microcredentialpoint <= completedHomeworkPoints) {
            this.sharedService.addPoints(
              {
                memberId: memberId,
                impactScore: Number(microcredentialpoint),
                MVPTokens: Number(microcredentialpoint),
                pointType: 'microcredential',
                status: 'CREDITED',
                type: entityType,
                cityId: this.sharedService.defaultCityId.value,
                createdBy: this.loggedInUserId,
                category: microcredentialType,
                categoryID: this.categoryList[microcredentialType?.trim()] ?? Object.values(this.categoryList)[0]
              },
            ).subscribe({
              next: (result => {
                this.transferMVT(memberId, microcredentialpoint);
                this.sharedService.updateMembership({
                  id: membershipId,
                  currentImpactScore: parseFloat(currentImpactScore) + microcredentialpoint,
                  MVPTokens: parseFloat(MVPTokens) + (microcredentialpoint),
                  lastAddedImpactScore: microcredentialpoint,
                  _version: version,
                });
              })
            })
          }

        })
      });
    }
  }

  triggerFileInput(): void {
    this.audioInputRef.nativeElement.click();
  }

  onAudioSelected(event: Event): void {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (!file) return;

    const formData = new FormData();
    formData.append('audio', file);

    this.http.post('/api/upload-audio', formData).subscribe({
      next: (res) => console.log('Upload success', res),
      error: (err) => console.error('Upload failed', err),
    });
  }

  updatePost(): void {
    this.spinner.show();
    this.sharedService.isLoading.next(true);

    let formData: any = this.storyForm.value;

    this.handlePointsAssignment(formData);

    this.storyForm.get('gradedBy')?.patchValue(this.loggedInUserName);

    this.postService.updatePost({ id: this.storyId, ...this.storyForm.value }).subscribe({
      next: (res: any) => this.handleUpdateResponse(res, formData),
      error: (err: any) => {
        this.spinner.hide();
        this.sharedService.isLoading.next(false);
      }
    });
  }

  private handlePointsAssignment(formData: any) {
    if (
      this.storyForm.get('submissionStatus')?.value === 'APPROVED' &&
      !this.postDetails?.pointsAssigned
    ) {
      formData.pointsAssigned = true;
    }
  }

  private handleUpdateResponse(res: any, formData: any) {
    const submission = res?.data?.updateSubmission;
    const homework = submission?.homework;

    const homeworkMembers = this.findMatchingItem(homework?.members?.items, formData.projectId, formData.memberId, 'homeworkId', 'memberId');
    const homeworkUsers = this.findMatchingItem(homework?.studentsStakeholders?.items, formData.projectId, formData.memberId, 'homeworkId', 'studentStakeholderId');

    this.uploadFilesBasedOnMediaType();

    this.updateStoriesMembers(submission.memberId, 'UPDATE', submission.pointsAssigned);

    switch (submission.submissionStatus) {
      case 'APPROVED':
        this.processApprovedStatus(homeworkUsers, homeworkMembers, submission);
        break;
      case 'INREVIEW':
        this.processInReviewStatus(homeworkUsers, homeworkMembers, submission);
        break;
      case 'DENIED':
        this.processDeniedStatus(homeworkUsers, homeworkMembers);
        break;
    }

    this.generateLogAndFinish(submission);
  }

  private findMatchingItem(items: any[], projectId: any, memberId: any, homeworkIdKey: string, memberIdKey: string) {
    if (!items) return null;
    return items.find(item => item[homeworkIdKey] === projectId && item[memberIdKey] === memberId) ?? null;
  }

  private uploadFilesBasedOnMediaType() {
    if (this.mediaType === 'image') {
      for (let fileData of this.uploadImages) {
        const fileName = `image_cropper_${fileData?.lastModified}.${fileData?.name.split('.')[1]}`;
        this.uploadFile(fileData, fileName, 'image', 'false');
      }
    } else if (this.mediaType === 'audio') {
      const file = this.files[0];
      const fileName = `AUD_${file?.lastModified}.${file?.name.split('.')[1]}`;
      this.uploadFile(file, fileName, 'audio', 'false');
    } else {
      const file = this.files[0];
      const fileName = `VID_${file?.lastModified}.${file?.name.split('.')[1]}`;
      this.uploadFile(file, fileName, 'video', 'false');
      if (this.videoThumbnailFile.length > 0 && this.thumbnailName) {
        this.uploadFile(this.videoThumbnailFile[0], this.thumbnailName, 'video', 'true');
      }
    }
  }

  private processApprovedStatus(homeworkUsers: any, homeworkMembers: any, submission: any) {
    if (homeworkUsers?.id) {
      this.postService.updateHomeworkUsers({
        id: homeworkUsers.id,
        homeworkStatus: 'completed',
        homeworkCompletedDate: new Date().toISOString(),
        _version: homeworkUsers._version
      }).subscribe(response => {
        this.addSubmissionPoints(response, 'user');
      });
    } else if (homeworkMembers?.id) {
      this.postService.updateHomeworkOrganizations({
        id: homeworkMembers.id,
        homeworkStatus: 'completed',
        homeworkCompletedDate: new Date().toISOString(),
        _version: homeworkMembers._version
      }).subscribe(response => {
        this.addSubmissionPoints(response, 'member');
      });
    }

    this.handleKnowledgeAndTranscript(submission);

    // Additional transcription trigger for approved submissions
    this.triggerTranscriptionForApproval(submission);
  }

  private processInReviewStatus(homeworkUsers: any, homeworkMembers: any, submission: any) {
    if (homeworkUsers?.id) {
      this.postService.updateHomeworkUsers({
        id: homeworkUsers.id,
        homeworkStatus: 'in-review',
        _version: homeworkUsers._version
      }).subscribe(response => {
        this.createSubmissionActivityLog(response, submission, 'user');
      });
    } else if (homeworkMembers?.id) {
      this.postService.updateHomeworkOrganizations({
        id: homeworkMembers.id,
        homeworkStatus: 'in-review',
        _version: homeworkMembers._version
      }).subscribe(response => {
        this.createSubmissionActivityLog(response, submission, 'member');
      });
    }
  }

  private processDeniedStatus(homeworkUsers: any, homeworkMembers: any) {
    if (homeworkUsers?.id) {
      this.postService.updateHomeworkUsers({
        id: homeworkUsers.id,
        homeworkStatus: 'denied',
        _version: homeworkUsers._version
      }).subscribe(response => {
        this.handleDeniedActivities(response?.data?.updateHomeworkUsers?.studentStakeholderId);
        this.sendMessage(response?.data?.updateHomeworkUsers?.homeworkData?.name, 'deny', response?.data?.updateHomeworkUsers?.studentStakeholderData);
      });
    } else if (homeworkMembers?.id) {
      this.postService.updateHomeworkOrganizations({
        id: homeworkMembers.id,
        homeworkStatus: 'denied',
        _version: homeworkMembers._version
      }).subscribe(response => {
        this.handleDeniedActivities(response?.data?.updateHomeworkOrganizations?.memberId);
      });
    }
  }

  private handleDeniedActivities(memberOrUserId: any) {
    this.postService.getActivitiesList(this.storyId, memberOrUserId).subscribe(result => {
      const activities = result?.data?.activitiesByDate?.items ?? [];
      for (const activity of activities) {
        const activityUpdate = {
          id: activity?.id,
          requestStatus: 'ADMIN_DENIED',
          _version: activity?._version,
        };
        this.sharedService.updateActivity(activityUpdate).subscribe();
      }
    });
  }

  private handleKnowledgeAndTranscript(submission: any) {
    if (!submission) return;
    if (!submission.videos.length && !submission.audios.length) return;
    if (submission.homework?.microcredential?.categoryID === undefined) return;

    const fileUrl = submission.videos.length
      ? `post/${submission.id}/videos/${submission.videos[0]}`
      : `post/${submission.id}/audios/${submission.audios[0]}`;

    const isOrgOrMember = submission.projectType === "organization" || submission.projectType === "member";

    const submissionData: any = {
      categoryId: submission.homework.microcredential.categoryID,
      subCategoryIds: [],
      fileUrl,
      fileType: submission.videos.length ? 'VIDEO' : 'AUDIO',
      knowledgeEntityType: isOrgOrMember ? "ORGANIZATION" : "STAKEHOLDER",
      name: submission.text,
      description: submission.description ?? '',
      durationInMinutes: submission.videoDuration ?? 0,
      submittedBy: submission.createdBy,
      ...(isOrgOrMember ? { organizationId: submission.memberId } : { userId: submission.memberId }),
    };

    this.addToStakeholderKnowledge(submissionData);
    this.saveVideoOrAudioTranscript(submission.id, fileUrl);
  }

  private generateLogAndFinish(submission: any) {
    const memberId = submission?.memberId;

    this.sharedService.generateLog({
      type: 'UPDATED',
      moduleId: submission?.id,
      moduleName: submission?.text ?? 'story',
      moduleType: 'story',
      relatedTo: this.type === 'organization' ? 'member' : this.type,
      relatedId: memberId ?? '',
      relatedName: this.selectedMemberName?.name,
      requestStatus: 'SYSTEM_APPROVED',
      moduleImageUrl: this.userImage ? this.userImage : '',
      activityType: 'STORYTELLING',
      cityId: this.sharedService.defaultCityId.value,
      activityTokens: '25',
    }).subscribe(() => {
      this.toastr.success('Submission updated successfully!');
      this.spinner.hide();
      this.sharedService.isLoading.next(false);
      this.router.navigate(['/activity-submission']);
    });
  }

  createStoryPoints(storyElement: any): void {
    let selectedMemberName: any;
    if (this.type === 'organization') {
      const selectedItem = this.organizationList.find(
        (item) => item.id === storyElement?.memberId
      );
      selectedMemberName = selectedItem ? selectedItem.name : '';
    } else if (this.type === 'business') {
      const selectedItem = this.businessList.find(
        (item) => item.id === storyElement?.memberId
      );
      selectedMemberName = selectedItem ? selectedItem.name : '';
    } else {
      const selectedItem = this.personsList.find(
        (item) => item.id === storyElement?.memberId
      );
      selectedMemberName = selectedItem ? selectedItem.name : '';
    }

    this.sharedService.addPoints(storyElement).subscribe({
      next: (res: any) => {
        this.transferMVT(storyElement.memberId, storyElement.impactScore);
        this.sendMessage(selectedMemberName, '', '');
      },
      error: (err: any) => { },
    });
  }

  sendMessage(memberName: any, subType: any, userData: any): void {
    let userList: any;
    let formData: any;

    if (subType === 'deny') {
      formData = {
        title:
          memberName + " homework has been denied by admin.",
        body: '',
        feedbackId: 'null',
        notificationType: 'homework-submission',
        taskNotificationsId: 'null',
        notificationIcon: 'notificationIcons/' + this.sharedService?.warningNotificationIconName,
      };
      userList = [{ endpointArn: userData?.endpointArn, id: userData?.id, isLogin: userData?.isLogin }]
      formData.userList = userList;
      this.sharedService.notificationSend(formData).subscribe((res: any) => { });
    } else {
      formData = {
        title:
          'You have received ' + (userData?.homeworkData?.assignmentPoints) + ' MVP tokens for submitting ' + memberName + ' assignment.',
        body: '',
        feedbackId: 'null',
        notificationType: 'points',
        taskNotificationsId: 'null',
        points: "100",
        MVPTokens: "25",
        notificationIcon: 'notificationIcons/' + this.sharedService?.mvpNotificationIconName,
      };

      userList = this.allUsers.filter((element) => element).map(({ endpointArn, id, isLogin }) => ({
        endpointArn,
        id,
        isLogin
      }));
      formData.userList = userList;
      this.sharedService.notificationSend(formData).subscribe((res: any) => { });
    }



  }

  updateStoriesMembers(
    memberId: any,
    storyOperation: any,
    pointsAssigned: any
  ): void {
    if (pointsAssigned) {
      if (this.type === 'organization') {
        if (
          storyOperation === 'CREATE' ||
          (storyOperation === 'UPDATE' &&
            this.storyForm.get('memberId')?.dirty) ||
          this.postDetails?.pointsAssigned !== true
        ) {
          this.createStoryPoints({
            memberId: this.storyForm.get('memberId')?.value,
            impactScore: this.sharedService?.pointForStoryAndProject,
            MVPTokens: parseFloat(this.sharedService?.pointForStoryAndProject),
            pointType: 'story',
            type: 'member',
            status: 'CREDITED',
            cityId: this.sharedService.defaultCityId.value,
            createdBy: this.loggedInUserId,
            category: this.storyForm.get('categoryType')?.value,
            categoryID: this.categoryList[this.storyForm.get('categoryType')?.value.trim()] ?? Object.values(this.categoryList)[0]
          });

          this.sharedService
            .getMember(memberId, this.type)
            .subscribe((res: any) => {
              let currentImpactScore =
                parseFloat(
                  res?.data?.membershipByDate?.items[0].currentImpactScore
                ) + parseFloat(this.sharedService?.pointForStoryAndProject);
              let lastImpactScore = parseFloat(
                this.sharedService?.pointForStoryAndProject
              );
              let memberId = res?.data?.membershipByDate?.items[0].id;
              this.sharedService.updateMembership({
                id: memberId,
                cityId: res?.data?.membershipByDate?.items[0].cityId,
                organizationID:
                  res?.data?.membershipByDate?.items[0].organizationID,
                type: 'member',
                name: res?.data?.membershipByDate?.items[0].name,
                shortDescription:
                  res?.data?.membershipByDate?.items[0].shortDescription,
                imageUrl: res?.data?.membershipByDate?.items[0].imageUrl,
                currentImpactScore: currentImpactScore,
                MVPTokens: parseFloat(res?.data?.membershipByDate?.items[0].MVPTokens + (parseFloat(this.sharedService.pointForStoryAndProject) * this.sharedService?.tokenBaseValue)).toFixed(2),
                lastAddedImpactScore: lastImpactScore,
                _version: res?.data?.membershipByDate?.items[0]._version,
              });
            });
        }
      }
      else if (this.type === 'person') {
        if (
          storyOperation === 'CREATE' ||
          (storyOperation === 'UPDATE' && this.storyForm.get('memberId')?.dirty)
        ) {
          this.createStoryPoints({
            memberId: this.storyForm.get('memberId')?.value,
            impactScore: this.sharedService?.pointForStoryAndProject,
            MVPTokens: parseFloat(this.sharedService?.pointForStoryAndProject) * this.sharedService?.tokenBaseValue,
            pointType: 'story',
            status: 'CREDITED',
            type: 'student',
            cityId: this.sharedService.defaultCityId.value,
            createdBy: this.loggedInUserId,
            category: this.storyForm.get('categoryType')?.value,
            categoryID: this.categoryList[this.storyForm.get('categoryType')?.value.trim()] ?? Object.values(this.categoryList)[0]
          });

          this.sharedService.getMember(memberId, 'person').subscribe((res) => {
            if (res?.data?.membershipByDate?.items) {
              let currentImpactScore =
                Math.floor(
                  res?.data?.membershipByDate?.items[0].currentImpactScore
                ) + parseFloat(this.sharedService?.pointForStoryAndProject);
              let membership = res?.data?.membershipByDate?.items[0];
              if (membership) {
                membership.currentImpactScore = currentImpactScore;
                membership.MVPTokens = parseFloat(res?.data?.membershipByDate?.items[0].MVPTokens + (parseFloat(this.sharedService.pointForStoryAndProject) * this.sharedService?.tokenBaseValue)).toFixed(2);
                membership.lastAddedImpactScore = parseFloat(this.sharedService?.pointForStoryAndProject);
                delete membership?.updatedAt;
                delete membership?._deleted;
                delete membership?._lastChangedAt;
                delete membership?.organization;
                delete membership?.person;
                this.personsService
                  .editMembership({
                    ...membership,
                  })
                  .subscribe();
              }
            }
          });
        }
      }

    }
  }

  private async addToStakeholderKnowledge(submissionData: any): Promise<void> {
    try {
      await this.stakeholderKnowledgeService.createKnowledgeRepositoryStore(submissionData);
      if (submissionData.knowledgeEntityType === "ORGANIZATION") {
        await this.stakeholderKnowledgeService.updateKnowledgeSummaryForOrganization(submissionData.organizationId, submissionData.categoryId, submissionData.durationInMinutes);
      } else {
        await this.stakeholderKnowledgeService.updateKnowledgeSummary(submissionData.userId, submissionData.categoryId, submissionData.durationInMinutes);
      }
    } catch (error) {
      console.error('Error adding to stakeholder knowledge:', error);
    }
  }

  private async saveVideoOrAudioTranscript(id: string, fileUrl: string) {
    try {
      const transcriptResult = await this.transcribeService.fetchVideoOrAudioTranscript(fileUrl, id, "submission");
      // You can now use transcriptResult.transcriptText, transcriptResult.transcriptDetail, transcriptResult.transcription
      // Example: show transcript in UI, save to state, etc.
      // console.log('Transcript:', transcriptResult.transcriptText);
    } catch (error) {
      console.error('Error adding to stakeholder knowledge:', error);
    }
  }

  // Trigger transcription for approved submission
  private async triggerTranscriptionForApproval(submission: any): Promise<void> {
    try {
      // Check if submission has video or audio content
      const hasMedia = (submission.videos && submission.videos.length > 0) ||
                      (submission.audios && submission.audios.length > 0);

      if (!hasMedia) {
        console.log('No media files found for transcription');
        return;
      }

      // Check if transcription already exists to avoid duplicate processing
      if (submission.transcriptDetail) {
        console.log('Transcription already exists for submission:', submission.id);
        return;
      }

      console.log('Starting transcription process for approved submission:', submission.id);

      // Trigger transcription via posts service
      await this.postService.triggerTranscriptionForApprovedSubmission(
        submission.id,
        submission.videos ?? [],
        submission.audios ?? []
      );

      console.log('Transcription triggered successfully for submission:', submission.id);

    } catch (error) {
      console.error('Error triggering transcription for approved submission:', error);
      this.toastr.error('Failed to start transcription process');
    }
  }

  // Method to fetch wallet address based on member ID
  async getWalletAddress(memberId: string): Promise<string> {
    try {
      // Get user details using personsService
      const response = await lastValueFrom(this.personsService.getUserById(memberId));
      const userData = response?.data?.getUser;

      // Return wallet address if available, otherwise use memberId as fallback
      if (userData?.walletAddress && userData.walletAddress.length > 0) {
        return userData.walletAddress;
      } else {
        return memberId; // Fallback to using memberId
      }
    } catch (error) {
      console.error("Error fetching wallet address:", error);
      return memberId; // Fallback to using memberId on error
    }
  }

  // Helper method to transfer MVT tokens using MVT wallet backend
  async transferMVT(memberId: string, amount: number): Promise<void> {
    try {
      console.log(`Transferring ${amount} MVT tokens to member ${memberId} using MVT Wallet backend...`);

      this.mvtWalletService.adminTransferMVT(memberId, amount, `Admin transferred ${amount} MVT tokens for submission approval`).subscribe({
        next: (response: any) => {
          console.log("MVT transfer success:", response);

          if (response?.data?.adminTransferMVT?.statusCode === 200) {
            console.log("MVT transfer completed successfully");

            // Log transaction details if available
            if (response.data.adminTransferMVT.data) {
              console.log('Transfer Transaction Details:', {
                id: response.data.adminTransferMVT.data.id,
                transactionHash: response.data.adminTransferMVT.data.transactionHash,
                amount: response.data.adminTransferMVT.data.amount,
                status: response.data.adminTransferMVT.data.status,
                transactionType: response.data.adminTransferMVT.data.transactionType
              });
            }
          } else {
            console.error("MVT transfer failed:", response?.data?.adminTransferMVT?.message);
          }
        },
        error: (error: any) => {
          console.error("MVT transfer error:", error);
          const errorMessage = error?.message || error?.error?.message || 'Failed to transfer MVT tokens';
          console.error("Transfer error details:", errorMessage);
        }
      });
    } catch (error) {
      console.error("Error in MVT token transfer:", error);
    }
  }

}

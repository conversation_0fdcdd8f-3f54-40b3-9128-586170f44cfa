<app-toolbar class="toolbar" [showBack]="true">
</app-toolbar>

<div class="card mb-5 mb-xl-10 position-relative">
  <div class="d-flex m-0 p-5 position-absolute justify-content-end end-0">
    <span class="btn btn-outline-primary align-self-center p-2 pe-0" (click)="navigateForEdit(personDetails.id)"
      (keydown.enter)="navigateForEdit(personDetails.id)">
      <span class="svg-icon svg-icon-1">
        <app-svg-general svg="gen055"></app-svg-general>
      </span>
    </span>
    <span class="btn btn-outline-danger align-self-center p-2 pe-0"
      (click)="openDeleteConfirmationModal(modal, personDetails?.id, personDetails?.givenName+ ' ' + personDetails?.familyName)"
      (keydown.enter)="openDeleteConfirmationModal(modal, personDetails?.id, personDetails?.givenName+ ' ' + personDetails?.familyName)">
      <span class="svg-icon svg-icon-1 rotate-180">
        <app-svg-general svg="gen027"></app-svg-general>
      </span>
    </span>
  </div>
  <div class="card-body p-9 pb-0">
    <div class="d-flex flex-wrap flex-sm-nowrap mb-7">
      <div class="me-7 mb-4">
        <div class="symbol symbol-100px symbol-lg-160px symbol-fixed position-relative">
          <span *ngIf="!personDetails?.imagePreview" class="symbol-label fs-4x fw-bold text-primary bg-light-primary">
            {{ (personDetails?.givenName?.trim().charAt(0) | camelcase) || "P" }}
          </span>
          <img class="cursor-pointer" [src]="personDetails?.imagePreview" *ngIf="!!personDetails?.imagePreview"
            (error)="sharedService.onImgError($event)" alt="User detail Logo">
        </div>
      </div>
      <div class="flex-grow-1">
        <div class="d-flex justify-content-between align-items-start flex-wrap mb-2">
          <div class="d-flex flex-column">
            <div class="d-flex align-items-center mb-2">
              <span class="text-gray-900 text-hover-primary fs-2 fw-bold me-1">
                {{ (personDetails?.givenName+ ' ' + personDetails?.familyName) | camelcase }}
              </span>
              <!-- Add stakeholder/student badge -->
              <span class="badge fs-8 fw-bold ms-2" *ngIf="personDetails?.isStakeholder !== undefined"
                [ngClass]="{'badge-light-primary': personDetails?.isStakeholder, 'badge-light-info': !personDetails?.isStakeholder}">
                {{ personDetails?.isStakeholder ? 'Stakeholder' : 'Student' }}
              </span>
            </div>
            <div class="d-flex flex-wrap fw-semibold fs-6 mb-4 pe-2">
              <span class="d-flex align-items-center text-gray-400 me-5 mb-2">
                {{ personDetails?.email }}
              </span>
            </div>
          </div>
        </div>
        <div class="d-flex flex-wrap flex-stack">
          <div class="d-flex flex-column flex-grow-1 pe-8">
            <div class="d-flex flex-wrap">
              <div class="border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3">
                <div class="d-flex align-items-center">
                  <div class="fs-3 fw-bold counted"> {{ (personDetails?.gender|titlecase) ||'-'}} </div>
                </div>
                <div class="fw-semibold fs-6 text-gray-400"> Gender </div>
              </div>
              <div class="border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3"
                [hidden]="!isStudent">
                <div class="d-flex align-items-center">
                  <div class="fs-3 fw-bold counted"> {{ personDetails?.ethnicity || '-' }} </div>
                </div>
                <div class="fw-semibold fs-6 text-gray-400"> Ethnicity </div>
              </div>
              <div class="border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3">
                <div class="d-flex align-items-center">
                  <div *ngIf="personDetails?.phoneNumber" class="fs-3 fw-bold counted">
                    {{personDetails?.status|titlecase}}
                  </div>
                  <div *ngIf="personDetails?.status === '' || personDetails?.status === null"
                    class="fs-3 fw-bold counted">
                    -
                  </div>
                </div>
                <div class="fw-semibold fs-6 text-gray-400"> Status </div>
              </div>
              <div class="border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3">
                <div class="d-flex align-items-center">
                  <div class="fs-3 fw-bold counted">
                    {{ getDisplayPoints() }}
                  </div>
                  <i class="bi bi-award-fill ms-2 text-warning fs-4"></i>
                </div>
                <div class="fw-semibold fs-6 text-gray-400"> Points </div>
              </div>
              <!-- Enhanced MVT Token Card - Only show for stakeholders -->
              <div *ngIf="personDetails?.isStakeholder"
                class="border border-gray-300 border-dashed rounded mvt-balance-card py-3 px-4 me-6 mb-3 position-relative"
                [class.opacity-50]="!personDetails?.id">

                <!-- Loading State -->
                <div *ngIf="isLoadingMVT" class="d-flex align-items-center justify-content-center">
                  <div class="spinner-border spinner-border-sm text-primary me-2">
                    <span class="visually-hidden">Loading...</span>
                  </div>
                  <span class="text-muted">Loading balance...</span>
                </div>

                <!-- Balance Data -->
                <div *ngIf="!isLoadingMVT && mvtWalletBalance" class="mvt-balance-container">
                  <!-- Primary Balance Section -->
                  <div class="primary-balance-section">
                    <div class="d-flex align-items-center justify-content-between">
                      <div class="d-flex align-items-center">
                        <div class="fs-3 fw-bold counted text-primary">
                          {{ mvtWalletBalance.balance | number:'1.0-2' }}
                        </div>
                        <span class="badge bg-primary text-white ms-2">MVT</span>
                      </div>
                      <!-- Wallet Info Icon with Hover Details -->
                      <i class="bi bi-wallet2 text-muted cursor-pointer wallet-info-icon" data-bs-toggle="tooltip"
                        data-bs-placement="top" [attr.title]="getBalanceTooltip()">
                      </i>
                    </div>
                  </div>

                  <!-- Secondary Balance Information -->
                  <div class="secondary-balance-section mt-2">
                    <!-- Locked Balance Display - Only show if there are locked tokens -->
                    <div *ngIf="hasLockedBalance()"
                         class="locked-balance-row mb-1">
                      <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                          <div class="fs-6 fw-semibold text-warning me-2">
                            {{ mvtWalletBalance.lockedBalance | number:'1.0-2' }}
                          </div>
                          <span class="badge bg-warning text-dark locked-badge">
                            <i class="bi bi-lock-fill me-1"></i>Locked
                          </span>
                        </div>
                        <!-- Locked Balance Info Icon with Tooltip -->
                        <i class="bi bi-info-circle locked-info-icon"
                           data-bs-toggle="tooltip"
                           data-bs-placement="top"
                           title="MVT tokens temporarily locked for pending swap requests. These tokens cannot be transferred until the swap is completed or cancelled.">
                        </i>
                      </div>
                    </div>

                    <!-- Available Balance Display -->
                    <div class="available-balance-row">
                      <small class="available-balance">
                        Available: {{ getAvailableBalance() | number:'1.0-2' }} MVT
                      </small>
                    </div>
                  </div>
                </div>

                <!-- Error State -->
                <div *ngIf="!isLoadingMVT && !mvtWalletBalance" class="d-flex align-items-center">
                  <div class="fs-3 fw-bold counted text-muted">
                    0
                  </div>
                  <span class="badge bg-secondary text-white ms-2">MVT</span>
                  <span class="badge bg-warning text-white ms-2">
                    <i class="bi bi-exclamation-triangle me-1" data-bs-toggle="tooltip" data-bs-placement="top"
                      title="Unable to load balance" aria-label="Unable to load balance"></i>
                  </span>
                </div>

                <div class="fw-semibold fs-6 text-gray-400 mt-2">
                  MVT wallet balance
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="d-flex overflow-auto h-55px">
      <ul class="nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-5 fw-bolder flex-nowrap">
        <li class="nav-item">
          <p class="nav-link text-active-primary text-hover-primary me-6 cursor-pointer"
            [class.active]="currentTab === 'overview'" (click)="setCurrentTab('overview')"
            (keydown.enter)="setCurrentTab('overview')">
            Overview
          </p>
        </li>
        <li class="nav-item">
          <p class="nav-link text-active-primary text-hover-primary me-6 cursor-pointer"
            [class.active]="currentTab === 'description'" (click)="setCurrentTab('description')"
            (keydown.enter)="setCurrentTab('description')">
            {{isStudent?"Details":"Description"}}
          </p>
        </li>
        <li class="nav-item">
          <p class="nav-link text-active-primary text-hover-primary me-6 cursor-pointer"
            [class.active]="currentTab === 'homework'" (click)="setCurrentTab('homework')"
            (keydown.enter)="setCurrentTab('homework')">
            Activities
          </p>
        </li>
        <li class="nav-item">
          <p class="nav-link text-active-primary text-hover-primary me-6 cursor-pointer"
            [class.active]="currentTab === 'projects'" (click)="setCurrentTab('projects')"
            (keydown.enter)="setCurrentTab('projects')">
            Projects
          </p>
        </li>
        <li class="nav-item">
          <p class="nav-link text-active-primary text-hover-primary me-6 cursor-pointer"
            [class.active]="currentTab === 'association'" (click)="setCurrentTab('association')"
            (keydown.enter)="setCurrentTab('association')">
            Associations
          </p>
        </li>
        <li class="nav-item">
          <p class="nav-link text-active-primary text-hover-primary me-6 cursor-pointer"
            [class.active]="currentTab === 'repository'" (click)="setCurrentTab('repository')"
            (keydown.enter)="setCurrentTab('repository')">
            Repository
          </p>
        </li>
        <li class="nav-item">
          <p class="nav-link text-active-primary text-hover-primary me-6 cursor-pointer"
            [class.active]="currentTab === 'logs'" (click)="setCurrentTab('logs')"
            (keydown.enter)="setCurrentTab('logs')">
            Logs
          </p>
        </li>
        <!-- Transactions tab - Show for all users -->
        <li class="nav-item">
          <p class="nav-link text-active-primary text-hover-primary me-6 cursor-pointer"
            [class.active]="currentTab === 'transactions'" (click)="setCurrentTab('transactions')"
            (keydown.enter)="setCurrentTab('transactions')">
            Token Transactions
          </p>
        </li>
      </ul>
    </div>
  </div>
</div>

<div [hidden]="currentTab !== 'overview'">
  <div class="row g-5 g-lg-10">
    <div class="col-xl-6 mb-5 mb-xl-10">
      <div class="card h-xl-100">
        <app-mixed-widget7 [chartColor]="chartColor" chartHeight="165px"
          [memberPercentageList]="memberPercentageList"></app-mixed-widget7>
      </div>
    </div>
    <div class="col-xl-6 mb-5 mb-xl-10">
      <div class="card h-xl-100">
        <app-persons-tasks-widget [personId]="personId"></app-persons-tasks-widget>
      </div>
    </div>
  </div>

  <div class="row g-5 g-lg-10">
    <div class="col-xl-6 mb-5 mb-xl-10">
      <div class="card h-xl-100">
        <app-persons-events-widget></app-persons-events-widget>
      </div>
    </div>
    <div class="col-xl-6 mb-5 mb-xl-10">
      <div class="card h-xl-100">
        <app-person-knowledge-repository [personId]="personId"
          [isStudent]="isStudent"></app-person-knowledge-repository>
      </div>
    </div>
  </div>
</div>

<div class="card mb-5 mb-xl-10" *ngIf="currentTab === 'repository'">
  <div class="card-body">
    <app-repository-list [personId]="personId" [isStudent]="isStudent"></app-repository-list>
  </div>
</div>

<div *ngIf="isStudent">
  <div class="card mb-5 mb-xl-10" *ngIf="currentTab === 'description'">
    <div class="card-body p-9 py-2">
      <!-- header-start -->
      <div class="row my-7">
        <p class="h3 card-title align-items-start flex-column">
          <span class="card-label fw-bold text-dark">
            Student Info
          </span>
        </p>
      </div>
      <!-- header-end -->
      <div class="row my-7">
        <label class="col-lg-4 fw-bold text-muted" for=""> Gender </label>
        <div class="col-lg-8">
          <span class="fw-bold fs-6 text-dark">{{personDetails?.gender??"-"}} </span>
        </div>
      </div>
      <div class="row my-7">
        <label class="col-lg-4 fw-bold text-muted" for=""> Ethnicity </label>
        <div class="col-lg-8">
          <span class="fw-bold fs-6 text-dark">{{personDetails?.ethnicity??"-"}} </span>
        </div>
      </div>
      <div class="row my-7">
        <label class="col-lg-4 fw-bold text-muted" for=""> Member Code </label>
        <div class="col-lg-8">
          <span class="fw-bold fs-6 text-dark">{{personDetails?.memberCode??"-"}} </span>
        </div>
      </div>
      <div class="row my-7">
        <label class="col-lg-4 fw-bold text-muted" for=""> Birthday </label>
        <div class="col-lg-8">
          <span class="fw-bold fs-6 text-dark">{{personDetails?.birthday??"-"}} </span>
        </div>
      </div>
      <div class="row my-7">
        <label class="col-lg-4 fw-bold text-muted" for=""> Enrollment Status </label>
        <div class="col-lg-8">
          <span class="fw-bold fs-6 text-dark">{{(personDetails?.status??"-") | titlecase}} </span>
        </div>
      </div>
      <div class="row my-7">
        <label class="col-lg-4 fw-bold text-muted" for=""> Wallet Address </label>
        <div class="col-lg-8">
          <span class="fw-bold fs-6 text-dark">{{personDetails?.walletAddress ?? '-'}}</span>
        </div>
      </div>
    </div>
  </div>
  <div class="card mb-5 mb-xl-10" *ngIf="currentTab === 'description'">
    <div class="card-body p-9 py-2">
      <!-- header-start -->
      <div class="row my-7">
        <p class="h3 card-title align-items-start flex-column">
          <span class="card-label fw-bold text-dark">
            Contact Info
          </span>
        </p>
      </div>
      <!-- header-end -->
      <div class="row my-7">
        <label class="col-lg-4 fw-bold text-muted" for=""> Phone Number </label>
        <div class="col-lg-8">
          <span class="fw-bold fs-6 text-dark">{{personDetails?.phoneNumber??"-"}} </span>
        </div>
      </div>
      <div class="row my-7">
        <label class="col-lg-4 fw-bold text-muted" for=""> Email </label>
        <div class="col-lg-8">
          <span class="fw-bold fs-6 text-dark">{{personDetails?.email??"-"}} </span>
        </div>
      </div>
      <div class="row my-7">
        <label class="col-lg-4 fw-bold text-muted" for=""> Address </label>
        <div class="col-lg-8">
          <span class="fw-bold fs-6 text-dark">
            <ng-container *ngIf="!personDetails?.streetAddressOne && !personDetails?.streetAddressTwo
                                && !personDetails?.city && !personDetails?.state && !personDetails?.zipCode">
              -
            </ng-container>
            <ng-container *ngIf="personDetails?.streetAddressOne">
              {{ personDetails?.streetAddressOne }},
            </ng-container>
            <ng-container *ngIf="personDetails?.streetAddressTwo">
              {{ personDetails?.streetAddressTwo }},
            </ng-container>
            <ng-container *ngIf="personDetails?.city">
              {{ personDetails?.city }},
            </ng-container>
            <ng-container *ngIf="personDetails?.state">
              {{ personDetails?.state }},
            </ng-container>
            <ng-container *ngIf="personDetails?.zipCode">
              {{ personDetails?.zipCode }}
            </ng-container>
          </span>
        </div>
      </div>
    </div>
  </div>
  <div class="card mb-5 mb-xl-10" *ngIf="currentTab === 'description'">
    <div class="card-body p-9 py-2">
      <!-- header-start -->
      <div class="row my-7">
        <p class="h3 card-title align-items-start flex-column">
          <span class="card-label fw-bold text-dark">
            Category Data
          </span>
        </p>
      </div>
      <!-- header-end -->
      <div class="row my-7" *ngFor="let item of listCategories">
        <label class="col-lg-4 fw-bold text-muted" for=""> {{item}} </label>
        <div class="col-lg-8">
          <span class="fw-bold fs-6 text-dark"> {{ categoryWisePoints[item] }} </span>
        </div>
      </div>
    </div>
  </div>
</div>
<div *ngIf="!isStudent">
  <div class="card mb-5 mb-xl-10" *ngIf="currentTab === 'description'">
    <div class="card-body p-9 py-2">
      <div class="row my-7">
        <label class="col-lg-4 fw-bold text-muted" for="address"> Address </label>
        <div class="col-lg-8">
          <span class="fw-bold fs-6 text-dark">
            <ng-container *ngIf="!personDetails?.streetAddressOne && !personDetails?.streetAddressTwo
            && !personDetails?.city && !personDetails?.state && !personDetails?.zipCode">
              -
            </ng-container>
            <ng-container *ngIf="personDetails?.streetAddressOne">
              {{ personDetails?.streetAddressOne }},
            </ng-container>
            <ng-container *ngIf="personDetails?.streetAddressTwo">
              {{ personDetails?.streetAddressTwo }},
            </ng-container>
            <ng-container *ngIf="personDetails?.city">
              {{ personDetails?.city }},
            </ng-container>
            <ng-container *ngIf="personDetails?.state">
              {{ personDetails?.state }},
            </ng-container>
            <ng-container *ngIf="personDetails?.zipCode">
              {{ personDetails?.zipCode }}
            </ng-container>
          </span>
        </div>
      </div>
      <div class="row mb-7">
        <label class="col-lg-4 fw-bold text-muted" for=""> {{isStudent?'Enrollment Status':'Status'}} </label>
        <div class="col-lg-8">
          <span class="fw-bold fs-6 text-dark"> {{ (personDetails?.status === 'true' ? 'Active' :(personDetails?.status
            ===
            'false'?"Inactive":personDetails?.status)) | camelcase }} </span>
        </div>
      </div>
      <div class="row mb-7">
        <label class="col-lg-4 fw-bold text-muted" for=""> Member Code </label>
        <div class="col-lg-8">
          <span class="fw-bold fs-6 text-dark"> {{ personDetails?.memberCode }} </span>
        </div>
      </div>
      <div class="row mb-7" [hidden]="isStudent">
        <label class="col-lg-4 fw-bold text-muted" for=""> Role </label>
        <div class="col-lg-8">
          <span class="fw-bold fs-6 text-dark">
            {{ getRoleDisplayName(personDetails?.assignedRole) }}
          </span>
        </div>
      </div>
      <div class="row mb-7" [hidden]="isStudent">
        <label class="col-lg-4 fw-bold text-muted" for=""> System Role </label>
        <div class="col-lg-8">
          <span class="fw-bold fs-6 text-dark"> {{ personDetails?.role }} </span>
        </div>
      </div>
      <div class="row my-7">
        <label class="col-lg-4 fw-bold text-muted" for=""> Wallet Address </label>
        <div class="col-lg-8">
          <span class="fw-bold fs-6 text-dark">{{personDetails?.walletAddress ?? '-'}}</span>
        </div>
      </div>
    </div>
  </div>
  <div class="card mb-5 mb-xl-10" *ngIf="currentTab === 'description'">
    <div class="card-body p-9 py-2">
      <!-- header-start -->
      <div class="row my-7">
        <p class="h3 card-title align-items-start flex-column">
          <span class="card-label fw-bold text-dark">
            Category Data
          </span>
        </p>
      </div>
      <!-- header-end -->
      <div class="row my-7" *ngFor="let item of listCategories">
        <label class="col-lg-4 fw-bold text-muted" for=""> {{item}} </label>
        <div class="col-lg-8">
          <span class="fw-bold fs-6 text-dark"> {{ categoryWisePoints[item] }} </span>
        </div>
      </div>
    </div>
  </div>
</div>

<app-homework-list [personId]="personId" [hidden]="currentTab !== 'homework'"></app-homework-list>
<app-programs [personId]="personId" [hidden]="currentTab !== 'projects'"></app-programs>

<div class="row g-5 g-lg-10" *ngIf="currentTab === 'logs'">
  <div class="d-flex flex-column flex-lg-row">
    <div class="flex-lg-row-fluid">
      <div class="d-flex flex-wrap flex-stack pb-7">
        <div class="d-flex flex-wrap align-items-center my-1">
          <p class="h3 fw-bolder me-5 my-1" *ngIf="!updateProfile">
            {{ activitiesCount }} {{ activitiesCount === 1 ? "Activity" : "Activities" }}
          </p>
          <button type="button" class="btn btn-sm btn-secondary" *ngIf="updateProfile"
            (click)="showActivityList()">Back</button>
        </div>
      </div>
      <div class="tab-content" *ngIf="!updateProfile">
        <div class="tab-pane fade show active">
          <div class="card card-flush">
            <div class="card-body py-3">
              <div class="table-responsive">
                <table class="table table-row-bordered table-row-dashed gy-4 align-middle fw-bolder"
                  aria-describedby="posts-table">
                  <thead class="fs-7 text-gray-400 text-uppercase">
                    <tr>
                      <th class="min-w-400px"> Member & Activity </th>
                      <th class="min-w-150px text-center"> Activity Date </th>
                      <th class="min-w-100px text-center"> MVP Tokens </th>
                      <th class="min-w-150px text-center"> Type </th>
                      <th class="min-w-100px text-center"> Approval </th>
                    </tr>
                  </thead>
                  <tbody *ngIf="activitiesCount > 0">
                    <ng-container *ngFor="let activity of activitiesList;index as i;">

                      <tr>
                        <td class="fw-normal text-gray-800"
                          *ngIf="activity.moduleType!=='homework-submission' && activity.moduleType!=='update-profile' && activity.type!=='GAME' ">
                          <div class="d-flex align-items-center">
                            <div class="me-5 position-relative">
                              <div class="symbol symbol-40px symbol-circle">
                                <span
                                  [class]="'symbol-label fw-bold text-' + (getRandomColor(i)) + ' bg-light-' + (getRandomColor(i))">
                                  {{ (activity?.moduleName?.trim().charAt(0) | titlecase) || "O" }}
                                </span>
                              </div>
                            </div>
                            <div>
                              <ng-container
                                *ngIf="(!activity.relatedTo && !(activity.moduleType === 'project' && activity.isRelationship) &&   activity.moduleType !== 'funding' && activity.activityType!=='FUNDING' && activity.moduleType !== 'fundTransactions'   || activity.type === 'DELETED' &&  activity.relatedId && activity.type === 'GAME'|| (activity.moduleType === 'association' && activity.isRelationship && activity.type==='DELETED'))">
                                <strong>
                                  <a *ngIf="activity.moduleType === 'business'"
                                    [routerLink]="['/' + activity.moduleType + 'es/view-' + activity.moduleType, activity.moduleId]"
                                    class="text-gray-800 text-hover-primary text-break">
                                    {{ activity.moduleName | titlecase }}
                                  </a>
                                  <a *ngIf="(activity.moduleType !=='member' && activity.moduleType !=='business' && activity.moduleType !=='member' && activity.moduleType !== 'story' &&activity.moduleType !== 'homework'&&activity.moduleType !== 'update-profile') && activity.moduleType !=='association'"
                                    [routerLink]="['/' + (activity.moduleType==='student' && activity.moduleUser?.isStakeholder?'stakeholder':activity.moduleType) + 's/view-' + (activity.moduleType==='student' && activity.moduleUser?.isStakeholder?'stakeholder':activity.moduleType), activity.moduleId]"
                                    class="text-gray-800 text-hover-primary">
                                    {{ activity.moduleName | titlecase }}
                                  </a>
                                  <a *ngIf="(activity.moduleType === 'member' )"
                                    [routerLink]="['/' + activity.moduleType + 's/view-' + activity.moduleType, sharedService.getEncryptedId(activity.moduleId)]"
                                    class="text-gray-800 text-hover-primary">
                                    {{ activity.moduleName | titlecase }}
                                  </a>
                                  <a *ngIf="(activity.moduleType ==='story' )"
                                    [routerLink]="['/assigment-submission/view-submission', activity.moduleId]"
                                    class="text-gray-800 text-hover-primary">
                                    {{ activity.moduleName | titlecase }}
                                  </a>
                                  <a *ngIf="(activity.moduleType !=='member' && activity.moduleType === 'association') && activity.moduleType !== 'business'"
                                    (click)="sharedService.doNavigation(activity)"
                                    class="text-gray-800 text-hover-primary">
                                    {{ activity.moduleName | titlecase }}
                                  </a>
                                  <a *ngIf="(activity.moduleType ==='homework' )"
                                    [routerLink]="['/activities/view-activities/' +activity.moduleId]"
                                    class="text-gray-800 text-hover-primary">
                                    {{ activity.moduleName | titlecase }}
                                  </a>
                                  <a *ngIf="(activity.moduleType ==='update-profile' && activity?.moduleUser?.isStakeholder)"
                                    (click)="profileNavigation(activity,'/stakeholders/view-stakeholder/' +activity.moduleId)"
                                    class="text-gray-800 text-hover-primary">
                                    {{ activity.moduleName | titlecase }}
                                  </a>
                                  <a *ngIf="(activity.moduleType ==='update-profile' && !activity?.moduleUser?.isStakeholder)"
                                    (click)="profileNavigation(activity,'/students/view-student/' +activity.moduleId)"
                                    class="text-gray-800 text-hover-primary">
                                    {{ activity.moduleName | titlecase }}
                                  </a>

                                  <a *ngIf="(activity.type === 'TOKENS')"
                                    [routerLink]="['/activity-submission/view-submission', activity.moduleId]"
                                    class="text-gray-800 text-hover-primary">
                                    {{ activity.moduleName | titlecase }}
                                  </a>

                                </strong>
                                has been
                                <strong
                                  *ngIf="(!activity.isRelationship && !activity.relatedTo) || (activity.moduleType!=='association' && activity.type === 'DELETED')">
                                  <a *ngIf="(activity.moduleType ==='update-profile' && activity?.moduleUser?.isStakeholder)"
                                    (click)="profileNavigation(activity,'/stakeholders/view-stakeholder/' +activity.moduleId)"
                                    class="text-gray-800 text-hover-primary">
                                    {{ activity.type | lowercase }}
                                  </a>
                                  <a *ngIf="(activity.moduleType ==='update-profile' && !activity?.moduleUser?.isStakeholder)"
                                    (click)="profileNavigation(activity,'/students/view-student/' +activity.moduleId)"
                                    class="text-gray-800 text-hover-primary">
                                    {{ activity.type | lowercase }}
                                  </a>
                                  <span *ngIf="activity.moduleType!=='update-profile'">
                                    {{ activity.type | lowercase }}
                                  </span>
                                </strong>
                                <span *ngIf="activity.isRelationship">
                                  <span
                                    [innerHTML]="activity.type === 'UPDATED' ? '<strong> assigned new role </strong> in ' : activity.type === 'ADDED' ? '<strong> enrolled </strong> to ' :(activity.moduleType==='association' && activity.type === 'DELETED')?'<strong> disassociated </strong> from ': '<strong> discharged </strong> from '">
                                  </span>
                                  <strong>
                                    <a *ngIf="activity.moduleType === 'association'"
                                      (click)="sharedService.doNavigation(activity)"
                                      class="text-gray-800 text-hover-primary">
                                      {{ activity.relatedName | titlecase }}
                                    </a>
                                    <a *ngIf="activity.moduleType !== 'association'"
                                      [routerLink]="['/' + activity.relatedTo + 's/view-' + activity.relatedTo, activity.relatedId]"
                                      class="text-gray-800 text-hover-primary">
                                      {{ activity.relatedName | titlecase }}
                                    </a>
                                  </strong>
                                </span>.
                              </ng-container>
                              <ng-container
                                *ngIf="(activity.relatedTo && activity.relatedTo !== activity.moduleType && !(activity.isRelationship && activity.moduleType === 'project') && !(activity.type === 'DELETED' &&  activity.relatedId) && (activity?.type !== 'VIEWED' && activity?.type !== 'TOKENS') && activity?.moduleType!=='funding' &&  activity?.moduleType!=='fundTransactions' && activity.type !== 'GAME')  ">
                                Relationship between
                                <strong>
                                  <a (click)="sharedService.doNavigation(activity,'relatedName')"
                                    class="text-gray-800 text-hover-primary">
                                    {{ activity.relatedName | titlecase }}
                                  </a>
                                </strong>
                                &
                                <strong>
                                  <a (click)="sharedService.doNavigation(activity, 'moduleName')"
                                    class="text-gray-800 text-hover-primary">
                                    {{ activity.moduleName | titlecase }}
                                  </a>
                                </strong>
                                has been
                                <strong> {{ activity.type | lowercase }} </strong>.
                              </ng-container>
                              <ng-container
                                *ngIf="activity.moduleType === 'funding' && activity?.type !== 'TOKENS' && !activity.isRelationship">
                                The city
                                <strong>
                                  <a class="text-gray-800 text-hover-primary">
                                    {{ activity.moduleName | titlecase }}
                                  </a>
                                </strong>
                                has received a donation.
                              </ng-container>
                              <ng-container *ngIf="activity.activityType === 'FUNDING' && activity?.type === 'TOKENS'">
                                <strong>
                                  <a class="text-gray-800 text-hover-primary">
                                    {{ activity?.relatedName | titlecase }}
                                  </a>
                                </strong>
                                has received
                                <strong>
                                  <a class="text-gray-800 text-hover-primary">
                                    ${{ activity?.activityTokens}}
                                  </a>
                                </strong>
                                , invested by
                                <strong>
                                  <a class="text-gray-800 text-hover-primary">
                                    {{ activity?.moduleName | titlecase }}
                                  </a>
                                </strong>
                                for project
                                <strong>
                                  <a class="text-gray-800 text-hover-primary">
                                    {{ activity?.updatedData?.givenName | titlecase }}
                                  </a>
                                </strong>.
                              </ng-container>
                              <ng-container
                                *ngIf="activity.moduleType === 'funding' && activity?.type !== 'TOKENS' && activity.isRelationship">
                                The city
                                <strong>
                                  <a class="text-gray-800 text-hover-primary">
                                    {{ activity.moduleName | titlecase }}
                                  </a>
                                </strong>
                                has disbursed funds to
                                <strong>
                                  <a (click)="sharedService.doNavigation(activity, 'relatedName')"
                                    class="text-gray-800 text-hover-primary">
                                    {{ activity.relatedName | titlecase }}
                                  </a>
                                </strong>
                                .
                              </ng-container>
                              <ng-container
                                *ngIf="(activity.moduleType === 'project' && activity.isRelationship) && activity?.type !== 'TOKENS'">
                                <strong>
                                  <a [routerLink]="['/' + activity.moduleType + 's/view-' + activity.moduleType, activity.moduleId]"
                                    class="text-gray-800 text-hover-primary">
                                    {{ activity.moduleName | titlecase }}
                                  </a>
                                </strong>
                                has been
                                <strong> {{ activity.type === 'ADDED' ? 'scheduled' : 'cancelled' }} </strong>
                                for
                                <strong>
                                  <a *ngIf="activity?.relatedTo === 'member'" (click)="navigateToOrganization(activity)"
                                    class="text-gray-800 text-hover-primary">
                                    {{ activity.relatedName | titlecase }}
                                  </a>
                                  <a *ngIf="activity?.relatedTo !== 'member'"
                                    [routerLink]="['/' + activity.relatedTo + 's/view-' + activity.relatedTo, activity.relatedId]"
                                    class="text-gray-800 text-hover-primary">
                                    {{ activity.relatedName | titlecase }}
                                  </a>
                                </strong>.
                              </ng-container>
                              <ng-container *ngIf="activity.moduleType==='fundTransactions' && activity.type==='ADDED'">
                                <strong>
                                  <a class="text-gray-800 text-hover-primary">
                                    {{ activity.moduleName | titlecase }}
                                  </a>
                                </strong>
                                imported transactions in
                                <strong>
                                  <a class="text-gray-800 text-hover-primary">
                                    {{ cityData[activity.cityId] | titlecase }}
                                  </a>
                                </strong>
                                city.
                              </ng-container>
                              <ng-container *ngIf="activity.type === 'VIEWED'">
                                <strong>
                                  <a [routerLink]="['/activity-submission/view-submission/' + activity.moduleId]"
                                    class="text-gray-800 text-hover-primary">
                                    {{ activity.moduleName | titlecase }}
                                  </a>
                                </strong>
                                has been
                                <strong> viewed </strong>
                              </ng-container>
                              <br />

                              <div class="fw-normal fs-6 text-gray-400">
                                By <strong> {{ activity.createdUserName }} </strong>
                              </div>
                            </div>
                          </div>
                        </td>
                        <td class="fw-normal text-gray-800" *ngIf="activity.moduleType==='homework-submission'">
                          <div class="d-flex align-items-center">
                            <div class="me-5 position-relative">
                              <div class="symbol symbol-40px symbol-circle">
                                <span
                                  [class]="'symbol-label fw-bold text-' + (getRandomColor(i)) + ' bg-light-' + (getRandomColor(i))">
                                  {{ (activity?.relatedName?.trim().charAt(0) | titlecase) || "O" }}
                                </span>
                              </div>
                            </div>
                            <div>
                              <strong><a class="text-gray-800 text-hover-primary text-break"
                                  (click)="sharedService.doNavigation(activity,'relatedName')">{{activity?.relatedName
                                  || '
                                  '}}</a></strong>
                              has
                              submitted their homework <strong><a
                                  [routerLink]="['/activity-submission/view-submission', activity.moduleId]"
                                  class="text-gray-800 text-hover-primary text-break">{{activity.moduleName}}</a></strong>.
                              <br />

                              <div class="fw-normal fs-6 text-gray-400">
                                By <strong> {{ activity.createdUserName }} </strong>
                              </div>
                            </div>
                          </div>
                        </td>
                        <td class="fw-normal text-gray-800" *ngIf="activity.moduleType==='update-profile'">
                          <div class="d-flex align-items-center">
                            <div class="me-5 position-relative">
                              <div class="symbol symbol-40px symbol-circle">
                                <span
                                  [class]="'symbol-label fw-bold text-' + (getRandomColor(i)) + ' bg-light-' + (getRandomColor(i))">
                                  {{ (activity?.moduleName?.trim().charAt(0) | titlecase) || "O" }}
                                </span>
                              </div>
                            </div>
                            <div *ngIf="activity?.moduleUser?.isStakeholder">
                              <strong><a class="text-gray-800 text-hover-primary text-break cursor-pointer"
                                  (click)="profileNavigation(activity,'/stakeholders/view-stakeholder/' +activity.moduleId)">{{activity?.moduleName
                                  || '
                                  '}}</a></strong>
                              has
                              requested <strong><a
                                  (click)="profileNavigation(activity,'/stakeholders/view-stakeholder/' +activity.moduleId)"
                                  class="text-gray-800 text-hover-primary text-break cursor-pointer">{{activity.relatedName}}</a></strong>.
                              <br />

                              <!-- Display updated fields in diff-style -->
                              <div *ngIf="activity?.updatedData && activity?.moduleUser" class="mt-2">
                                <div class="mb-1">
                                  <span class="text-gray-600 fw-normal">
                                    Update:
                                  </span>
                                </div>
                                <ng-container *ngFor="let key of (activity?.updatedData | keyvalue)">
                                  <ng-container *ngIf="key.key !== 'id' && activity.moduleUser[key.key] !== key.value">
                                    <div class="fw-bold text-dark fs-8">
                                      <span class="text-gray-800">{{ key.key | titlecase }}: </span>
                                      <span class="text-gray-600">{{ key.value || '-' }}</span>
                                    </div>
                                  </ng-container>
                                </ng-container>
                              </div>

                              <div class="fw-normal fs-6 text-gray-400">
                                By <strong> {{ activity.createdUserName }} </strong>
                              </div>
                            </div>
                            <div *ngIf="!activity?.moduleUser?.isStakeholder">
                              <strong><a class="text-gray-800 text-hover-primary text-break"
                                  (click)="profileNavigation(activity,'/students/view-student/' +activity.moduleId)">{{activity?.moduleName
                                  || '
                                  '}}</a></strong>
                              has
                              requested <strong><a
                                  (click)="profileNavigation(activity,'/students/view-student/' +activity.moduleId)"
                                  class="text-gray-800 text-hover-primary text-break">{{activity.relatedName}}</a></strong>.
                              <br />

                              <!-- Display updated fields in diff-style -->
                              <div *ngIf="activity?.updatedData && activity?.moduleUser" class="mt-2">
                                <div class="mb-1">
                                  <span class="text-gray-600 fw-normal">
                                    Update:
                                  </span>
                                </div>
                                <ng-container *ngFor="let key of (activity?.updatedData | keyvalue)">
                                  <ng-container *ngIf="key.key !== 'id' && activity.moduleUser[key.key] !== key.value">
                                    <div class="fw-bold text-dark fs-8">
                                      <span class="text-gray-800">{{ key.key | titlecase }}: </span>
                                      <span class="text-gray-600">{{ key.value || '-' }}</span>
                                    </div>
                                  </ng-container>
                                </ng-container>
                              </div>

                              <div class="fw-normal fs-6 text-gray-400">
                                By <strong> {{ activity.createdUserName }} </strong>
                              </div>
                            </div>

                          </div>
                        </td>
                        <td class="fw-normal text-gray-800" *ngIf="activity?.type==='GAME'">
                          <div class="d-flex align-items-center">
                            <div class="me-5 position-relative">
                              <div class="symbol symbol-40px symbol-circle">
                                <span
                                  [class]="'symbol-label fw-bold text-' + (getRandomColor(i)) + ' bg-light-' + (getRandomColor(i))">
                                  {{ (activity?.moduleName?.trim().charAt(0) | titlecase) || "O" }}
                                </span>
                              </div>
                            </div>
                            <div>
                              <strong><a class="text-gray-800 text-hover-primary text-break">{{activity?.moduleName
                                  || '
                                  '}}</a></strong>
                              has
                              logged in to <strong><a
                                  class="text-gray-800 text-hover-primary text-break">{{activity?.gameData?.gameName}}</a></strong>.
                              <br />

                              <div class="fw-normal fs-6 text-gray-400">
                                By <strong> {{ activity?.moduleName }}</strong>
                              </div>
                            </div>
                          </div>
                        </td>


                        <td class="fs-6 text-center"> <span>{{ activity.createdAt | date: 'MMM d, y' }}</span>
                          <p>{{ activity.createdAt | date: 'h:mm a' }}</p>
                        </td>
                        <td class="fs-6 text-center"> {{ activity?.activityTokens || '-'}} </td>
                        <td class="fs-6 text-center">
                          <button class="btn btn-secondary fw-medium text-hover-white" [ngClass]="{'btn-light-warning ': activity.activityType === 'MEMBERSHIP', 
                            'btn-light-success ': activity.activityType === 'PROJECT' || activity.activityType === 'FUNDING', 
                            'btn-light-danger ': activity.activityType === 'STORYTELLING', 
                            'btn-light-info ': ''
                            }">
                            {{activity?.activityType | titlecase}}
                          </button>
                        </td>
                        <td class="text-center w-225">
                          <button class="btn btn-secondary text-gray-500 fw-medium w-100"
                            *ngIf="activity?.requestStatus === 'SYSTEM_APPROVED' || activity?.requestStatus === null">
                            System Approved </button>
                          <button class="btn btn-success fw-medium w-100"
                            *ngIf="activity?.requestStatus === 'ADMIN_APPROVED'"> Admin Approved </button>
                          <button class="btn btn-danger fw-medium w-100"
                            *ngIf="activity?.requestStatus === 'ADMIN_DENIED'"> Admin Denied </button>
                          <div class="modal-footer justify-content-center"
                            *ngIf="activity?.requestStatus === 'PENDING_APPROVAL'">
                            <button type="button" class="btn btn-primary" style="margin-right: 6px;padding:10px"
                              (click)="updateActivity('ADMIN_APPROVED', activity)"> Approve </button>
                            <button type="button" class="btn btn-danger" style="padding:10px"
                              (click)="updateActivity('ADMIN_DENIED', activity)"> Decline </button>
                          </div>
                        </td>
                      </tr>
                    </ng-container>
                  </tbody>
                  <tbody class="fs-6" *ngIf="activitiesCount === 0">
                    <tr>
                      <td colspan="5" class="text-center">
                        <p class="text-gray-800 fw-bold mb-0"> No Records Found! </p>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <app-pagination [hidden]="activitiesCount === 0" [recordCount]="activitiesCount"></app-pagination>
        </div>
      </div>
      <div class="row g-5 g-lg-10" *ngIf="updateProfile">
        <div class="col-xl-6 mb-5 mb-xl-10">
          <div class="card h-xl-100 py-2 p-9 ">
            <h4 class="my-7">Old Person Profile.</h4>
            <div class="row my-7">
              <label class="col-lg-4 fw-bold text-muted" for=""> Full Name</label>
              <label class="col-lg-4 fw-bold text-muted" for="">{{personDetails?.givenName}}
                {{personDetails?.familyName}}</label>
            </div>
            <div class="row my-7">
              <label class="col-lg-4 fw-bold text-muted" for=""> Email</label>
              <label class="col-lg-4 fw-bold text-muted" for="">{{personDetails?.email}} </label>
            </div>

            <div class="row my-7">
              <label class="col-lg-4 fw-bold text-muted" for=""> Phone Number</label>
              <label class="col-lg-4 fw-bold text-muted" for="">{{personDetails?.phoneNumber}}</label>
            </div>

            <div class="row my-7">
              <label class="col-lg-4 fw-bold text-muted" for="">City</label>
              <label class="col-lg-4 fw-bold text-muted" for="">{{oldCityName}}</label>
            </div>

            <div class="row my-7">
              <label class="col-lg-4 fw-bold text-muted" for=""> Birthday</label>
              <label class="col-lg-4 fw-bold text-muted" for="">{{personDetails?.birthday}}</label>
            </div>
            <div class="row my-7">
              <label class="col-lg-4 fw-bold text-muted" for=""> Gender</label>
              <label class="col-lg-4 fw-bold text-muted" for="">{{personDetails?.gender}}</label>
            </div>

            <!-- Old wallet address field -->
            <div class="row my-7">
              <label class="col-lg-4 fw-bold text-muted" for="">Wallet Address</label>
              <label class="col-lg-4 fw-bold text-muted" for="">{{personDetails?.walletAddress}}</label>
            </div>

            <div class="row my-7">
              <label class="col-lg-4 fw-bold text-muted" for="">ImageUrl</label>
              <div class="col-lg-8">
                <div class="fw-bold fs-6 text-dark mb-3" *ngFor="let fileUrl of oldImagesPreview; let i=index">

                  <a *ngIf="!checkFileExtension(personDetails?.imageUrl)" class="text-dark text-hover-primary"
                    [href]="fileUrl">
                    -
                  </a>
                  <a *ngIf="checkFileExtension(personDetails?.imageUrl)"
                    class="text-dark text-hover-primary cursor-pointer"
                    (click)="openImageCarouselModal(imagesModal, fileUrl)" (error)="sharedService.onImgError($event)">
                    {{ i + 1 }})
                    {{personDetails?.imageUrl.substring(personDetails?.imageUrl.lastIndexOf("/") + 1,
                    personDetails?.imageUrl.length)}}
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-xl-6 mb-5 mb-xl-10">
          <div class="card h-xl-100 py-2 p-9">
            <h4 class="my-7">Update Person Profile.</h4>
            <div class="row my-7">
              <label class="col-lg-4 fw-bold text-muted" for=""> Full Name</label>
              <label class="col-lg-4 fw-bold text-muted" for="">{{updatedData?.updatedData.givenName}}
                {{updatedData?.updatedData.familyName}}</label>
            </div>
            <div class="row my-7">
              <label class="col-lg-4 fw-bold text-muted" for=""> Email</label>
              <label class="col-lg-4 fw-bold text-muted" for="">{{updatedData?.updatedData.email}} </label>
            </div>

            <div class="row my-7">
              <label class="col-lg-4 fw-bold text-muted" for=""> Phone Number</label>
              <label class="col-lg-4 fw-bold text-muted" for="">{{updatedData?.updatedData.phoneNumber}} </label>
            </div>

            <div class="row my-7">
              <label class="col-lg-4 fw-bold text-muted" for="">City</label>
              <label class="col-lg-4 fw-bold text-muted" for="">{{updatedCityName}}</label>
            </div>

            <div class="row my-7">
              <label class="col-lg-4 fw-bold text-muted" for="">Birthday</label>
              <label class="col-lg-4 fw-bold text-muted" for="">{{updatedData?.updatedData.birthday}}</label>
            </div>

            <div class="row my-7">
              <label class="col-lg-4 fw-bold text-muted" for="">Gender</label>
              <label class="col-lg-4 fw-bold text-muted" for="">{{updatedData?.updatedData.gender}}</label>
            </div>

            <!-- New wallet address field -->
            <div class="row my-7">
              <label class="col-lg-4 fw-bold text-muted" for="">Wallet Address</label>
              <label class="col-lg-4 fw-bold text-muted" for="">{{updatedData?.updatedData.walletAddress}}</label>
            </div>

            <div class="row my-7">
              <label class="col-lg-4 fw-bold text-muted" for="">ImageUrl</label>
              <div class="col-lg-8">
                <div class="fw-bold fs-6 text-dark mb-3" *ngFor="let fileUrl of updatedImagesPreview; let i=index">

                  <a *ngIf="!checkFileExtension(updatedData?.updatedData?.imageUrl)"
                    class="text-dark text-hover-primary" [href]="fileUrl">
                    -
                  </a>
                  <a *ngIf="checkFileExtension(updatedData?.updatedData?.imageUrl)"
                    class="text-dark text-hover-primary cursor-pointer"
                    (click)="openImageCarouselModal(imagesModal, fileUrl)" (error)="sharedService.onImgError($event)">
                    {{ i + 1 }})
                    {{updatedData?.updatedData?.imageUrl.substring(updatedData?.updatedData?.imageUrl.lastIndexOf("/") +
                    1,
                    updatedData?.updatedData?.imageUrl.length)}}
                  </a>
                </div>
              </div>
            </div>

            <div class="row my-7 fn-font">
              <label class="col-lg-4 fw-bold" for="">Status :
                <span
                  [ngClass]="{'fw-pending-color': updatedData?.requestStatus === 'PENDING_APPROVAL', 'fw-approve-color' : updatedData?.requestStatus === 'ADMIN_APPROVED' || updatedData?.requestStatus === 'SYSTEM_APPROVED', 'fw-reject-color' : updatedData?.requestStatus === 'ADMIN_DENIED' }">
                  <div *ngIf="updatedData?.requestStatus === 'PENDING_APPROVAL'">Pending Approval</div>
                  <div *ngIf="updatedData?.requestStatus === 'ADMIN_APPROVED'">Admin Approved</div>
                  <div *ngIf="updatedData?.requestStatus === 'ADMIN_DENIED'">Admin Denied</div>
                  <div *ngIf="updatedData?.requestStatus === 'SYSTEM_APPROVED'">System Approved</div>
                </span>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="alg-end" *ngIf="currentTab === 'logs' && updatedData?.requestStatus=== 'PENDING_APPROVAL'">
  <button type="button" class="btn btn-primary mar-right" (click)="updateStatus('Approved')">Approve</button>
  <button type="button" class="btn btn-danger"
    (click)="openRejectModel(rejectModel, updatedData?.id, updatedData?.moduleName, updatedData?.moduleType)">Reject</button>
</div>

<div class="card mb-5 mb-xl-10 bg-color" *ngIf="currentTab === 'association'">
  <div class="d-flex flex-wrap flex-stack pb-7">
    <div class="d-flex flex-wrap align-items-center my-1">
      <p class="h3 fw-bolder me-5 my-1">
      </p>
    </div>
  </div>
  <div class="card-body p-9 py-2">
    <div class="row my-7">
      <div class="tab-pane fade show">
        <div class="row g-6 g-xl-9">
          <ng-container *ngFor="let relationship of filteredAssociationList index as i">
            <div class="col-md-6 col-xl-12 col-xxl-6" *ngIf="(i >= (getCountStartValue - 1)) && (i < getCountEndValue)"
              (mouseover)="showOptions = relationship?.id" (mouseleave)="showOptions = ''"
              (focus)="showOptions = relationship?.id">
              <div class="card position-relative">
                <div class="d-flex m-0 p-5 position-absolute justify-content-end end-0"
                  *ngIf="showOptions === relationship?.id">
                  <a [routerLink]="['/families/edit-person-to-person', relationship?.id]"
                    class="btn btn-outline-primary align-self-center p-2 pe-0">
                    <span class="svg-icon svg-icon-1">
                      <app-svg-general svg="gen055"></app-svg-general>
                    </span>
                  </a>
                </div>
                <div class="card-body d-flex flex-center flex-column pt-15 p-9">
                  <div class="symbol symbol-65px symbol-circle mb-5">
                    <span
                      [class]="'symbol-label fs-2 fw-bold text-' + (getRandomColor(i)) + ' bg-light-' + (getRandomColor(i))">
                      {{ (relationship?.personName?.trim().charAt(0) | camelcase) +
                      (relationship?.otherRelationName?.trim().charAt(0) |
                      camelcase) || "P" }}
                    </span>
                  </div>
                  <p>
                    <span (click)="setFilters(relationship, 'person')"
                      (keydown.enter)="setFilters(relationship, 'person', $event)"
                      class="fs-4 text-gray-800 text-hover-primary fw-bolder mb-0 cursor-pointer">
                      {{ (relationship?.personName ) | camelcase }}
                    </span>
                    <strong> - </strong>
                    <span (click)="setFilters(relationship, 'otherPerson')"
                      (keydown.enter)="setFilters(relationship, 'otherPerson', $event)"
                      class="fs-4 text-gray-800 text-hover-primary fw-bolder mb-0 cursor-pointer">
                      {{ (relationship?.otherRelationName) | camelcase }}
                    </span>
                  </p>
                  <div class="d-flex flex-center flex-wrap">
                    <div class="border border-gray-300 border-dashed rounded min-w-80px py-3 px-4 mx-2 mb-3">
                      <div class="fs-6 fw-bolder text-gray-700"> {{ relationship?.relationType }}
                      </div>
                      <div class="fw-bold text-gray-400"> Relation Type </div>
                    </div>
                    <div class="border border-gray-300 border-dashed rounded min-w-80px py-3 px-4 mx-2 mb-3">
                      <div class="fs-6 fw-bolder text-gray-700">
                        {{ relationship?.status ? 'Active' : 'Inactive' }}
                      </div>
                      <div class="fw-bold text-gray-400"> Status </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </ng-container>
        </div>
        <div class="row g-6 g-xl-9" *ngIf="filteredAssociationList.length === 0">
          <div class="col-md-12 col-xl-12 col-xxl-12">
            <div class="card bg-color">
              <div class="card-body d-flex flex-center flex-column p-9">
                <a class="fs-4 text-gray-800 fw-bolder mb-0"> No Records Found! </a>
              </div>
            </div>
          </div>
        </div>
        <app-pagination [hidden]="filteredAssociationList.length === 0"
          [recordCount]="filteredAssociationList.length"></app-pagination>
      </div>
    </div>
  </div>
</div>

<!-- MVT Transactions component - Show for all users, let component handle no wallet case -->
<div *ngIf="currentTab === 'transactions'">
  <app-mvt-transactions [stakeholderWalletAddress]="personDetails?.id"
    [stakeholderName]="personDetails?.givenName + ' ' + personDetails?.familyName">
  </app-mvt-transactions>
</div>

<!-- -------------------- Member Delete Modal --------------------  -->
<ng-template #modal>
  <div class="modal-content" id="detailsModal">
    <div class="modal-header">
      <h2> Delete {{!router?.url?.includes('students') ? 'Stakeholder' : 'Student'}} </h2>
      <div class="btn btn-sm btn-icon btn-active-color-primary">
        <span class="cursor-pointer" (click)="closeDeleteConfirmationModal()"
          (keydown.enter)="closeDeleteConfirmationModal()">
          <span class="svg-icon svg-icon-1">
            <app-svg-arrow svg="arr061"></app-svg-arrow>
          </span>
        </span>
      </div>
    </div>
    <div class="modal-body text-center">
      <h5>
        <strong>
          Are you sure you want to delete
          <br />
          <span class="text-primary"> "{{ personName }}" </span>
          ?
        </strong>
      </h5>
    </div>
    <div class="modal-footer d-flex justify-content-center">
      <button type="button" class="btn btn-outline-primary" (click)="closeDeleteConfirmationModal()"> No </button>
      <button type="button" class="btn btn-outline-danger"
        (click)="isStudent?deletePerson():openDeleteConfirmationModalErase(modalConfirmation)"> Yes </button>
    </div>
  </div>
</ng-template>
<!-- -------------------- Member confirmation Delete Modal --------------------  -->
<ng-template #modalConfirmation>
  <div class="modal-content" id="detailsModalConfirmation">
    <div class="modal-header">
      <h2> Delete {{!router?.url?.includes('students') ? 'Stakeholder' : 'Student'}} </h2>
      <div class="btn btn-sm btn-icon btn-active-color-primary">
        <span class="cursor-pointer" (click)="closeDeleteConfirmationModal()"
          (keydown.enter)="closeDeleteConfirmationModal()">
          <span class="svg-icon svg-icon-1">
            <app-svg-arrow svg="arr061"></app-svg-arrow>
          </span>
        </span>
      </div>
    </div>
    <div class="modal-body text-center">
      <h5>
        <strong>
          Are you sure you want to delete
          <br />
          <span class="text-primary"> "{{ personName }}" </span>
          ?
        </strong><br>
        <strong class="text-danger">This will erase all the related data to the stakeholder.</strong>
      </h5>
    </div>
    <div class="modal-footer d-flex justify-content-center">
      <button type="button" class="btn btn-outline-primary" (click)="closeDeleteConfirmationModal()"> No </button>
      <button type="button" class="btn btn-outline-danger" (click)="deletePerson()"> Yes </button>
    </div>
  </div>
</ng-template>
<!-- -------------------- Task new profile Images Modal --------------------  -->
<ng-template #imagesModal>
  <div class="modal-content" id="detailsModal">
    <div class="modal-header">
      <h2> Task Images </h2>
      <div class="btn btn-sm btn-icon btn-active-color-primary">
        <span class="cursor-pointer" (click)="closeModal()" (keydown.enter)="closeModal()">
          <span class="svg-icon svg-icon-1">
            <app-svg-arrow svg="arr061"></app-svg-arrow>
          </span>
        </span>
      </div>
    </div>
    <div class="modal-body text-center">
      <img [src]="updateImages" alt="My description" style="width: 100%;" />
    </div>
    <div class="modal-footer d-flex justify-content-center">
      <button type="button" class="btn btn-outline-primary" (click)="closeModal()"> Close </button>
    </div>
  </div>
</ng-template>
<!-- -------------------- Task old profile Images Modal --------------------  -->
<ng-template #oldImagesModal>
  <div class="modal-content" id="detailsModal">
    <div class="modal-header">
      <h2> Task Images </h2>
      <div class="btn btn-sm btn-icon btn-active-color-primary">
        <span class="cursor-pointer" (click)="closeModal()" (keydown.enter)="closeModal()">
          <span class="svg-icon svg-icon-1">
            <app-svg-arrow svg="arr061"></app-svg-arrow>
          </span>
        </span>
      </div>
    </div>
    <div class="modal-body text-center">
      <ngb-carousel #carousel [interval]="2000" [activeId]="activeId">
        <ng-template ngbSlide *ngFor="let img of oldImagesPreview;  index as i" ngbSlide [id]="i.toString()">
          <div class="picsum-img-wrapper">
            <img [src]="img" alt="My pic {{i + 1}} description" style="width: 100%;" />
            max-height: 400px;
          </div>
        </ng-template>
      </ngb-carousel>
    </div>
    <div class="modal-footer d-flex justify-content-center">
      <button type="button" class="btn btn-outline-primary" (click)="closeModal()"> Close </button>
    </div>
  </div>
</ng-template>

<!-- -------------------- Reject Modal --------------------  -->
<ng-template #rejectModel>
  <div class="modal-content" id="detailsModal">
    <div class="modal-header">
      <h2> Reject Request </h2>
      <div class="btn btn-sm btn-icon btn-active-color-primary">
        <a class="cursor-pointer" (click)="closeModal()">
          <span class="svg-icon svg-icon-1">
            <app-svg-arrow svg="arr061"></app-svg-arrow>
          </span>
        </a>
      </div>
    </div>
    <div class="modal-body text-center">
      <h5>
        <strong>
          Are you sure you want to reject
          <span class="text-primary">
            {{ title }}
          </span>
        </strong>
      </h5>
    </div>
    <div class="modal-footer d-flex justify-content-center">
      <button type="button" class="btn btn-outline-primary" (click)="closeModal()"> No </button>
      <button type="button" class="btn btn-outline-danger" (click)="rejectTask('Rejected')"> Yes </button>
    </div>
  </div>
</ng-template>
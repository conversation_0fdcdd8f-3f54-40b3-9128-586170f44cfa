<!-- 
  Test file to verify the improved MVT balance visual design
  This demonstrates the expected layout and responsive behavior
-->

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MVT Balance Visual Design Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        /* Copy of the enhanced MVT balance styles */
        .mvt-balance-card {
            min-width: 280px;
            max-width: 320px;
            min-height: 85px;
        }
        
        @media (max-width: 1200px) {
            .mvt-balance-card {
                min-width: 260px;
                max-width: 300px;
            }
        }
        
        @media (max-width: 992px) {
            .mvt-balance-card {
                min-width: 240px;
                max-width: 280px;
            }
        }
        
        @media (max-width: 768px) {
            .mvt-balance-card {
                min-width: 200px;
                max-width: 100%;
                margin-right: 0 !important;
                margin-bottom: 1rem !important;
            }
        }

        .mvt-balance-container {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .primary-balance-section {
            flex: 0 0 auto;
            margin-bottom: 0.25rem;
        }

        .secondary-balance-section {
            flex: 1 1 auto;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }

        .locked-balance-row {
            border-top: 1px solid rgba(255, 193, 7, 0.15);
            padding: 0.4rem 0.5rem;
            margin-bottom: 0.3rem;
            background: rgba(255, 193, 7, 0.03);
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .locked-balance-row:hover {
            background: rgba(255, 193, 7, 0.06);
            border-color: rgba(255, 193, 7, 0.25);
        }

        .available-balance-row {
            margin-top: auto;
        }

        .available-balance {
            font-size: 0.75rem;
            color: #6c757d;
            font-weight: 500;
            line-height: 1.2;
            opacity: 0.8;
        }

        .wallet-info-icon, .locked-info-icon {
            transition: all 0.2s ease;
            opacity: 0.7;
            cursor: pointer;
        }

        .wallet-info-icon:hover, .locked-info-icon:hover {
            opacity: 1;
            transform: scale(1.1);
        }

        .locked-badge {
            font-size: 0.7rem;
            font-weight: 600;
            padding: 0.2rem 0.4rem;
        }

        /* Demo styles */
        .demo-container {
            padding: 2rem;
            background: #f8f9fa;
        }
        
        .demo-section {
            margin-bottom: 3rem;
        }
        
        .demo-title {
            margin-bottom: 1rem;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="text-center mb-5">MVT Balance Visual Design Test</h1>
        
        <!-- Test Case 1: Stakeholder with locked balance -->
        <div class="demo-section">
            <h3 class="demo-title">Stakeholder with Locked Balance</h3>
            <div class="d-flex flex-wrap">
                <!-- Other dashboard cards for comparison -->
                <div class="border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3">
                    <div class="d-flex align-items-center">
                        <div class="fs-3 fw-bold counted">Male</div>
                    </div>
                    <div class="fw-semibold fs-6 text-muted">Gender</div>
                </div>
                
                <div class="border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3">
                    <div class="d-flex align-items-center">
                        <div class="fs-3 fw-bold counted">Active</div>
                    </div>
                    <div class="fw-semibold fs-6 text-muted">Status</div>
                </div>
                
                <div class="border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3">
                    <div class="d-flex align-items-center">
                        <div class="fs-3 fw-bold counted">1224</div>
                        <i class="bi bi-award-fill ms-2 text-warning fs-4"></i>
                    </div>
                    <div class="fw-semibold fs-6 text-muted">Points</div>
                </div>
                
                <!-- Enhanced MVT Balance Card -->
                <div class="border border-gray-300 border-dashed rounded mvt-balance-card py-3 px-4 me-6 mb-3">
                    <div class="mvt-balance-container">
                        <!-- Primary Balance Section -->
                        <div class="primary-balance-section">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="d-flex align-items-center">
                                    <div class="fs-3 fw-bold counted text-primary">935.00</div>
                                    <span class="badge bg-primary text-white ms-2">MVT</span>
                                </div>
                                <i class="bi bi-wallet2 text-muted wallet-info-icon" title="Balance Details"></i>
                            </div>
                        </div>

                        <!-- Secondary Balance Information -->
                        <div class="secondary-balance-section mt-2">
                            <!-- Locked Balance Display -->
                            <div class="locked-balance-row mb-1">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex align-items-center">
                                        <div class="fs-6 fw-semibold text-warning me-2">15.00</div>
                                        <span class="badge bg-warning text-dark locked-badge">
                                            <i class="bi bi-lock-fill me-1"></i>Locked
                                        </span>
                                    </div>
                                    <i class="bi bi-info-circle locked-info-icon" title="Locked for pending swaps"></i>
                                </div>
                            </div>

                            <!-- Available Balance Display -->
                            <div class="available-balance-row">
                                <small class="available-balance">Available: 920.00 MVT</small>
                            </div>
                        </div>
                    </div>
                    <div class="fw-semibold fs-6 text-muted mt-2">MVT wallet balance</div>
                </div>
            </div>
        </div>
        
        <!-- Test Case 2: Stakeholder without locked balance -->
        <div class="demo-section">
            <h3 class="demo-title">Stakeholder without Locked Balance</h3>
            <div class="mvt-balance-card border border-gray-300 border-dashed rounded py-3 px-4">
                <div class="mvt-balance-container">
                    <!-- Primary Balance Section -->
                    <div class="primary-balance-section">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center">
                                <div class="fs-3 fw-bold counted text-primary">1250.00</div>
                                <span class="badge bg-primary text-white ms-2">MVT</span>
                            </div>
                            <i class="bi bi-wallet2 text-muted wallet-info-icon" title="Balance Details"></i>
                        </div>
                    </div>

                    <!-- Secondary Balance Information -->
                    <div class="secondary-balance-section mt-2">
                        <!-- Available Balance Display (no locked balance) -->
                        <div class="available-balance-row">
                            <small class="available-balance">Available: 1250.00 MVT</small>
                        </div>
                    </div>
                </div>
                <div class="fw-semibold fs-6 text-muted mt-2">MVT wallet balance</div>
            </div>
        </div>
        
        <!-- Test Case 3: Responsive behavior -->
        <div class="demo-section">
            <h3 class="demo-title">Responsive Design Test</h3>
            <p class="text-muted">Resize your browser window to see how the MVT balance card adapts to different screen sizes.</p>
            <div class="row">
                <div class="col-12 col-md-6 col-lg-4">
                    <div class="mvt-balance-card border border-gray-300 border-dashed rounded py-3 px-4 mb-3">
                        <div class="mvt-balance-container">
                            <div class="primary-balance-section">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex align-items-center">
                                        <div class="fs-3 fw-bold counted text-primary">750.50</div>
                                        <span class="badge bg-primary text-white ms-2">MVT</span>
                                    </div>
                                    <i class="bi bi-wallet2 text-muted wallet-info-icon"></i>
                                </div>
                            </div>
                            <div class="secondary-balance-section mt-2">
                                <div class="locked-balance-row mb-1">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <div class="d-flex align-items-center">
                                            <div class="fs-6 fw-semibold text-warning me-2">50.50</div>
                                            <span class="badge bg-warning text-dark locked-badge">
                                                <i class="bi bi-lock-fill me-1"></i>Locked
                                            </span>
                                        </div>
                                        <i class="bi bi-info-circle locked-info-icon"></i>
                                    </div>
                                </div>
                                <div class="available-balance-row">
                                    <small class="available-balance">Available: 700.00 MVT</small>
                                </div>
                            </div>
                        </div>
                        <div class="fw-semibold fs-6 text-muted mt-2">MVT wallet balance</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
